#!/usr/bin/env python3
"""
项目打包脚本
Project Packaging Script

按照要求打包项目为tar文件，包含：
1. 已commit到git的项目代码
2. MySQL数据库数据
"""

import os
import sys
import subprocess
import tarfile
import tempfile
import shutil
import mysql.connector
from datetime import datetime
from pathlib import Path

def check_git_status():
    """检查git状态"""
    print("🔍 检查Git状态...")
    
    try:
        # 检查是否在git仓库中
        result = subprocess.run(['git', 'status'], 
                              capture_output=True, text=True, cwd='.')
        
        if result.returncode != 0:
            print("❌ 当前目录不是Git仓库")
            return False
        
        # 检查是否有未提交的更改
        result = subprocess.run(['git', 'status', '--porcelain'], 
                              capture_output=True, text=True, cwd='.')
        
        if result.stdout.strip():
            print("⚠️ 发现未提交的更改:")
            print(result.stdout)
            response = input("是否继续打包？(y/N): ")
            if response.lower() != 'y':
                return False
        
        print("✅ Git状态检查完成")
        return True
        
    except Exception as e:
        print(f"❌ Git检查失败: {e}")
        return False

def export_git_archive(temp_dir):
    """导出git归档"""
    print("📦 导出Git归档...")
    
    try:
        # 获取当前分支
        result = subprocess.run(['git', 'branch', '--show-current'], 
                              capture_output=True, text=True, cwd='.')
        current_branch = result.stdout.strip()
        
        if not current_branch:
            current_branch = 'HEAD'
        
        print(f"   当前分支: {current_branch}")
        
        # 创建git归档
        git_archive_path = temp_dir / "project_code.tar"
        
        result = subprocess.run([
            'git', 'archive', 
            '--format=tar',
            f'--output={git_archive_path}',
            current_branch
        ], cwd='.')
        
        if result.returncode != 0:
            print("❌ Git归档导出失败")
            return None
        
        print(f"✅ Git归档已导出: {git_archive_path}")
        return git_archive_path
        
    except Exception as e:
        print(f"❌ Git归档导出失败: {e}")
        return None

def export_mysql_data(temp_dir):
    """导出MySQL数据"""
    print("🗄️ 导出MySQL数据...")
    
    try:
        # 数据库连接配置
        db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER', 'root'),
            'password': os.getenv('DB_PASSWORD', '123456'),
            'database': os.getenv('DB_NAME', 'stock')
        }
        
        print(f"   连接数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # 检查数据库连接
        connection = mysql.connector.connect(**db_config)
        connection.close()
        
        # 使用mysqldump导出数据
        dump_file = temp_dir / "mysql_data.sql"
        
        mysqldump_cmd = [
            'mysqldump',
            f'--host={db_config["host"]}',
            f'--port={db_config["port"]}',
            f'--user={db_config["user"]}',
            f'--password={db_config["password"]}',
            '--single-transaction',
            '--routines',
            '--triggers',
            '--add-drop-database',
            '--create-options',
            '--disable-keys',
            '--extended-insert',
            '--quick',
            '--lock-tables=false',
            db_config['database']
        ]
        
        with open(dump_file, 'w', encoding='utf-8') as f:
            result = subprocess.run(mysqldump_cmd, stdout=f, stderr=subprocess.PIPE, text=True)
        
        if result.returncode != 0:
            print(f"❌ MySQL数据导出失败: {result.stderr}")
            return None
        
        # 检查导出文件大小
        file_size = dump_file.stat().st_size
        print(f"✅ MySQL数据已导出: {dump_file} ({file_size:,} bytes)")
        
        return dump_file
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None
    except Exception as e:
        print(f"❌ MySQL数据导出失败: {e}")
        return None

def create_package_info(temp_dir):
    """创建打包信息文件"""
    print("📝 创建打包信息...")
    
    try:
        info_file = temp_dir / "package_info.txt"
        
        # 获取git信息
        git_commit = subprocess.run(['git', 'rev-parse', 'HEAD'], 
                                  capture_output=True, text=True).stdout.strip()
        git_branch = subprocess.run(['git', 'branch', '--show-current'], 
                                  capture_output=True, text=True).stdout.strip()
        
        # 获取项目信息
        project_root = Path(__file__).parent.parent
        
        info_content = f"""股票分析系统打包信息
Stock Analysis System Package Information

打包时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
项目路径: {project_root.absolute()}

Git信息:
- 分支: {git_branch}
- 提交: {git_commit}

包含内容:
1. project_code.tar - Git归档的项目代码
2. mysql_data.sql - MySQL数据库完整数据
3. package_info.txt - 本打包信息文件

部署说明:
1. 解压tar包
2. 导入MySQL数据: mysql -u root -p < mysql_data.sql
3. 解压项目代码: tar -xf project_code.tar
4. 启动应用: docker-compose up -d

访问信息:
- 应用地址: http://localhost:8501
- 登录账户: admin / admin123
- 数据库: localhost:3306/stock (root/123456)
"""
        
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write(info_content)
        
        print(f"✅ 打包信息已创建: {info_file}")
        return info_file
        
    except Exception as e:
        print(f"❌ 创建打包信息失败: {e}")
        return None

def create_final_package(temp_dir, git_archive, mysql_dump, package_info):
    """创建最终的tar包"""
    print("📦 创建最终tar包...")
    
    try:
        # 生成包名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        package_name = f"stock-analysis-system_{timestamp}.tar"
        
        # 创建tar包
        with tarfile.open(package_name, 'w') as tar:
            # 添加git归档
            tar.add(git_archive, arcname='project_code.tar')
            
            # 添加MySQL数据
            tar.add(mysql_dump, arcname='mysql_data.sql')
            
            # 添加打包信息
            tar.add(package_info, arcname='package_info.txt')
        
        # 检查包大小
        package_size = Path(package_name).stat().st_size
        print(f"✅ 最终包已创建: {package_name}")
        print(f"   包大小: {package_size:,} bytes ({package_size/1024/1024:.1f} MB)")
        
        return package_name
        
    except Exception as e:
        print(f"❌ 创建最终包失败: {e}")
        return None

def main():
    """主函数"""
    print("📦 股票分析系统项目打包工具")
    print("=" * 50)
    
    # 检查git状态
    if not check_git_status():
        sys.exit(1)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir_str:
        temp_dir = Path(temp_dir_str)
        print(f"📁 临时目录: {temp_dir}")
        
        # 导出git归档
        git_archive = export_git_archive(temp_dir)
        if not git_archive:
            sys.exit(1)
        
        # 导出MySQL数据
        mysql_dump = export_mysql_data(temp_dir)
        if not mysql_dump:
            sys.exit(1)
        
        # 创建打包信息
        package_info = create_package_info(temp_dir)
        if not package_info:
            sys.exit(1)
        
        # 创建最终包
        final_package = create_final_package(temp_dir, git_archive, mysql_dump, package_info)
        if not final_package:
            sys.exit(1)
        
        print("\n" + "=" * 50)
        print("🎉 打包完成！")
        print(f"📦 包文件: {final_package}")
        print("\n📋 包内容:")
        print("   - project_code.tar (Git归档的项目代码)")
        print("   - mysql_data.sql (MySQL数据库数据)")
        print("   - package_info.txt (打包信息)")
        print("\n💡 部署说明:")
        print("   1. 解压: tar -xf " + final_package)
        print("   2. 查看: cat package_info.txt")

if __name__ == "__main__":
    main()
