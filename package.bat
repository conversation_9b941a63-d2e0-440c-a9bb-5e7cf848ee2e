@echo off
chcp 65001 >nul
echo 📦 股票分析系统打包工具
echo ================================================

echo.
echo 选择打包方式:
echo 1. 完整打包 (包含Git代码 + MySQL数据)
echo 2. 简化打包 (仅包含项目代码)
echo.

set /p choice="请选择 (1 或 2): "

if "%choice%"=="1" (
    echo.
    echo 🔄 开始完整打包...
    python scripts/package_project.py
) else if "%choice%"=="2" (
    echo.
    echo 🔄 开始简化打包...
    python scripts/package_simple.py
) else (
    echo ❌ 无效选择，默认使用简化打包
    echo.
    echo 🔄 开始简化打包...
    python scripts/package_simple.py
)

echo.
echo ✅ 打包完成！
pause
