# 本地配置指南 Local Configuration Guide

本文档详细说明了如何根据本地环境配置股票分析系统。

## 📋 配置概览 Configuration Overview

### 本地环境要求 Local Environment Requirements

- **数据库名称**: `stock`
- **数据库用户**: `root`
- **数据库密码**: `123456`
- **数据库主机**: `localhost`
- **数据库端口**: `3306`

## 🔧 配置文件更改 Configuration File Changes

### 1. Docker Compose 配置

#### 主配置文件 (docker-compose.yml)
- 容器名称更新为 `stock_mysql` 和 `stock_app`
- 数据库名称改为 `stock`
- 移除了额外的MySQL用户，直接使用root用户
- 数据卷名称更新为 `stock_mysql_data`

#### 本地开发配置 (docker-compose.local.yml)
- 专门为本地开发环境创建的配置文件
- 包含开发调试相关的卷挂载
- 容器名称为 `stock_mysql_local` 和 `stock_app_local`

### 2. 数据库初始化脚本

#### scripts/init_database.sql
- 数据库名称从 `stock_analysis` 改为 `stock`

#### scripts/sample_data.sql
- 使用数据库从 `stock_analysis` 改为 `stock`

### 3. 应用配置

#### config/config.py
- 默认数据库名称改为 `stock`
- 测试数据库名称改为 `stock_test`

#### .env 文件
- 数据库名称更新为 `stock`
- 开启调试模式 (`DEBUG=True`)

## 🚀 使用方法 Usage

### 方式一：使用便捷脚本（推荐）

```bash
# 启动本地开发环境
python scripts/start_local.py

# 停止本地开发环境
python scripts/stop_local.py

# 检查配置状态
python scripts/check_config.py
```

### 方式二：使用Docker Compose

```bash
# 启动本地开发环境
docker-compose -f docker-compose.local.yml up -d

# 查看服务状态
docker-compose -f docker-compose.local.yml ps

# 查看日志
docker-compose -f docker-compose.local.yml logs -f

# 停止服务
docker-compose -f docker-compose.local.yml down
```

### 方式三：生产环境部署

```bash
# 使用标准配置
docker-compose up -d
```

## 📁 新增文件 New Files

1. **docker-compose.local.yml** - 本地开发环境配置
2. **scripts/start_local.py** - 本地环境启动脚本
3. **scripts/stop_local.py** - 本地环境停止脚本
4. **scripts/check_config.py** - 配置检查脚本
5. **docs/local-config-guide.md** - 本配置指南

## 🔍 配置验证 Configuration Verification

运行配置检查脚本来验证所有配置是否正确：

```bash
python scripts/check_config.py
```

该脚本会检查：
- 环境变量设置
- 配置文件状态
- 数据库连接
- Docker配置文件

## 🗄️ 数据库管理 Database Management

### 手动创建数据库

```sql
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE stock CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE stock;

-- 查看表
SHOW TABLES;
```

### 初始化数据库

```bash
# 运行初始化脚本
python scripts/init_db.py
```

## 🐛 故障排除 Troubleshooting

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 验证用户名和密码
   - 确认数据库名称正确

2. **容器启动失败**
   - 检查端口是否被占用
   - 查看Docker日志
   - 确认Docker服务运行正常

3. **数据库初始化失败**
   - 检查SQL脚本语法
   - 验证数据库权限
   - 查看错误日志

### 重置环境

```bash
# 停止所有服务
python scripts/stop_local.py

# 清理数据卷（可选，会删除所有数据）
docker volume rm stock_mysql_local_data

# 重新启动
python scripts/start_local.py
```

## 📞 技术支持 Technical Support

如果遇到配置问题：

1. 运行 `python scripts/check_config.py` 检查配置
2. 查看Docker日志：`docker-compose -f docker-compose.local.yml logs`
3. 检查数据库连接：`mysql -u root -p -h localhost`
4. 参考主README文档的故障排除部分

## 🎯 下一步 Next Steps

配置完成后，您可以：

1. 访问应用：http://localhost:8501
2. 使用默认账户登录：admin / admin123
3. 开始股票数据分析
4. 创建投资组合
5. 查看技术指标分析
