#!/usr/bin/env python3
"""
生成股票历史数据脚本
Generate Stock Historical Data Script

为股票分析系统生成5年的完整历史数据
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
import random
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', '123456'),
            database=os.getenv('DB_NAME', 'stock'),
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def generate_trading_dates(start_date, end_date):
    """生成交易日期（排除周末）"""
    dates = []
    current_date = start_date
    
    while current_date <= end_date:
        # 排除周末（周六=5, 周日=6）
        if current_date.weekday() < 5:
            dates.append(current_date)
        current_date += timedelta(days=1)
    
    return dates

def generate_stock_price_data(stock_code, stock_name, industry, start_price, dates):
    """生成股票价格数据"""
    data = []
    current_price = start_price
    
    for i, trade_date in enumerate(dates):
        # 模拟价格波动（随机游走 + 趋势）
        # 添加一些长期趋势和季节性
        trend_factor = 1 + (i / len(dates)) * 0.1  # 轻微上升趋势
        seasonal_factor = 1 + 0.05 * np.sin(2 * np.pi * i / 252)  # 年度季节性
        
        # 日收益率：正态分布，年化波动率约20%
        daily_return = np.random.normal(0.0005, 0.015)  # 约0.05%日均收益，1.5%日波动
        
        # 应用趋势和季节性
        daily_return *= trend_factor * seasonal_factor
        
        # 计算新价格
        new_price = current_price * (1 + daily_return)
        
        # 确保价格不会过低
        if new_price < start_price * 0.3:
            new_price = start_price * 0.3
        elif new_price > start_price * 3:
            new_price = start_price * 3
        
        # 生成开高低收价格
        volatility = abs(daily_return) + 0.005  # 当日波动率
        
        # 开盘价（基于前一日收盘价的小幅波动）
        open_price = current_price * (1 + np.random.normal(0, 0.005))
        
        # 收盘价
        close_price = new_price
        
        # 最高价和最低价
        high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, volatility/2)))
        low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, volatility/2)))
        
        # 确保价格逻辑正确
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        # 生成成交量（基于价格波动）
        base_volume = 50000000 + random.randint(-20000000, 30000000)
        volume_factor = 1 + abs(daily_return) * 10  # 波动大时成交量大
        volume = int(base_volume * volume_factor)
        
        # 成交额
        avg_price = (high_price + low_price + open_price + close_price) / 4
        amount = volume * avg_price
        
        # 换手率（简化计算）
        turnover_rate = (volume / 1000000000) * 100  # 假设总股本10亿股
        
        # 市盈率（简化计算）
        pe_ratio = close_price / (random.uniform(0.5, 2.0))  # 假设每股收益
        
        data.append({
            'stock_code': stock_code,
            'stock_name': stock_name,
            'industry': industry,
            'trade_date': trade_date,
            'open_price': round(open_price, 2),
            'close_price': round(close_price, 2),
            'high_price': round(high_price, 2),
            'low_price': round(low_price, 2),
            'volume': volume,
            'amount': round(amount, 2),
            'turnover_rate': round(turnover_rate, 2),
            'pe_ratio': round(pe_ratio, 2)
        })
        
        current_price = close_price
    
    return data

def insert_stock_data(connection, data):
    """插入股票数据到数据库"""
    cursor = connection.cursor()
    
    # 先清空现有数据
    cursor.execute("DELETE FROM stock_data")
    
    # 插入新数据
    insert_query = """
    INSERT INTO stock_data 
    (stock_code, stock_name, industry, trade_date, open_price, close_price, 
     high_price, low_price, volume, amount, turnover_rate, pe_ratio)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    
    batch_size = 1000
    for i in range(0, len(data), batch_size):
        batch = data[i:i + batch_size]
        batch_values = [
            (row['stock_code'], row['stock_name'], row['industry'], row['trade_date'],
             row['open_price'], row['close_price'], row['high_price'], row['low_price'],
             row['volume'], row['amount'], row['turnover_rate'], row['pe_ratio'])
            for row in batch
        ]
        
        cursor.executemany(insert_query, batch_values)
        connection.commit()
        print(f"已插入 {i + len(batch)} / {len(data)} 条记录")
    
    cursor.close()

def main():
    """主函数"""
    print("开始生成股票历史数据...")
    
    # 连接数据库
    connection = get_db_connection()
    if not connection:
        print("无法连接到数据库")
        return
    
    # 定义股票信息
    stocks = [
        {'code': '000001', 'name': '平安银行', 'industry': '银行', 'start_price': 10.0},
        {'code': '000002', 'name': '万科A', 'industry': '房地产开发', 'start_price': 15.0},
        {'code': '000858', 'name': '五粮液', 'industry': '白酒制造', 'start_price': 120.0},
        {'code': '600000', 'name': '浦发银行', 'industry': '银行', 'start_price': 8.0},
        {'code': '600036', 'name': '招商银行', 'industry': '银行', 'start_price': 25.0}
    ]
    
    # 生成日期范围（5年数据）
    end_date = date.today()
    start_date = end_date - timedelta(days=5*365)  # 5年前
    trading_dates = generate_trading_dates(start_date, end_date)
    
    print(f"生成日期范围: {start_date} 到 {end_date}")
    print(f"交易日总数: {len(trading_dates)}")
    
    # 为每只股票生成数据
    all_data = []
    for stock in stocks:
        print(f"正在生成 {stock['code']} - {stock['name']} 的数据...")
        stock_data = generate_stock_price_data(
            stock['code'], stock['name'], stock['industry'],
            stock['start_price'], trading_dates
        )
        all_data.extend(stock_data)
    
    print(f"总共生成 {len(all_data)} 条数据记录")
    
    # 插入数据库
    print("开始插入数据到数据库...")
    insert_stock_data(connection, all_data)
    
    connection.close()
    print("股票历史数据生成完成！")

if __name__ == "__main__":
    main()
