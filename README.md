# 📈 股票分析系统 Stock Analysis System

一个基于 Streamlit 和 MySQL 的专业股票数据分析平台，提供股票数据查询、技术指标分析、投资组合管理等功能。

A professional stock data analysis platform based on Streamlit and MySQL, providing stock data query, technical indicator analysis, portfolio management and other functions.

## ✨ 主要功能 Key Features

### 🔐 用户认证系统 User Authentication System

- **用户注册与登录**: 安全的用户注册和登录功能
- **密码加密**: 使用 bcrypt 进行密码哈希加密
- **会话管理**: 基于 Streamlit session_state 的会话管理
- **权限控制**: 用户访问权限控制

### 📊 股票数据分析 Stock Data Analysis

- **实时数据展示**: 显示股票的实时价格和基本信息
- **历史数据查询**: 支持自定义时间范围的历史数据查询
- **技术指标计算**:
  - 移动平均线 (MA): 5 日、10 日、20 日、60 日
  - 相对强弱指数 (RSI): 14 日
  - MACD 指标: 12,26,9 参数
  - 布林带 (Bollinger Bands): 20 日
- **K 线图展示**: 交互式 K 线图和技术指标图表

### 💼 投资组合管理 Portfolio Management

- **组合创建**: 创建和管理多个投资组合
- **持仓分析**: 详细的持仓分析和收益计算
- **风险评估**: 投资组合风险指标计算
- **收益率分析**:
  - 总收益率计算
  - 年化收益率
  - 最大回撤分析
  - 夏普比率计算

### 📈 市场概览 Market Overview

- **市场指数**: 主要市场指数实时显示
- **热门股票**: 热门股票排行榜
- **涨跌幅统计**: 市场涨跌幅分布统计
- **成交量分析**: 市场成交量趋势分析

## 🏗️ 技术架构 Technical Architecture

### 系统架构 System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit     │    │   Business      │    │   MySQL         │
│   Frontend      │◄──►│   Logic Layer   │◄──►│   Database      │
│   (用户界面)     │    │   (业务逻辑)     │    │   (数据存储)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈 Technology Stack

- **前端框架**: Streamlit 1.28+
- **后端语言**: Python 3.11+
- **数据库**: MySQL 8.0
- **数据处理**: Pandas, NumPy
- **图表库**: Plotly
- **容器化**: Docker & Docker Compose
- **密码加密**: bcrypt

### 项目结构 Project Structure

```
stock-analysis-app/
├── app/                    # 应用主目录
│   ├── main.py            # 应用入口文件
│   ├── auth/              # 认证模块
│   │   └── login.py       # 登录功能
│   ├── models/            # 数据模型
│   │   ├── user.py        # 用户模型
│   │   ├── stock.py       # 股票模型
│   │   └── portfolio.py   # 投资组合模型
│   ├── views/             # 页面视图
│   │   ├── dashboard.py   # 仪表板
│   │   ├── stock_analysis.py      # 股票分析
│   │   └── portfolio_analysis.py  # 投资组合分析
│   ├── database/          # 数据库模块
│   │   └── connection.py  # 数据库连接
│   └── utils/             # 工具函数
│       └── helpers.py     # 辅助函数
├── scripts/               # 脚本目录
│   ├── init_database.sql  # 数据库初始化脚本
│   ├── sample_data.sql    # 示例数据脚本
│   └── init_db.py         # 数据库初始化Python脚本
├── docs/                  # 文档目录
│   ├── api.md            # API文档
│   └── deployment.md     # 部署指南
├── config/                # 配置目录
│   └── config.py         # 配置文件
├── tests/                 # 测试目录
│   └── test_models.py    # 模型测试
├── docker-compose.yml     # Docker编排文件
├── Dockerfile            # Docker镜像文件
├── requirements.txt      # Python依赖
└── README.md            # 项目说明
```

## 🚀 快速开始 Quick Start

### 方式一：Docker 部署（推荐）

#### 前置要求 Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ 可用内存

#### 生产环境部署 Production Deployment

```bash
# 1. 克隆项目
git clone <repository-url>
cd stock-analysis-app

# 2. 启动服务
docker-compose up -d

# 3. 查看服务状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f
```

#### 本地开发环境部署 Local Development Deployment

```bash
# 1. 克隆项目
git clone <repository-url>
cd stock-analysis-app

# 2. 使用本地配置启动（数据库名：stock，用户：root）
docker-compose -f docker-compose.local.yml up -d

# 或者使用便捷脚本
python scripts/start_local.py

# 3. 查看服务状态
docker-compose -f docker-compose.local.yml ps

# 4. 停止服务
python scripts/stop_local.py
```

#### 访问应用 Access Application

- **应用地址**: http://localhost:8501
- **默认账户**: admin / admin123
- **本地数据库**:
  - 主机: localhost:3306
  - 数据库: stock
  - 用户名: root
  - 密码: 123456

### 方式二：本地开发部署

#### 环境要求 Environment Requirements

- Python 3.11+
- MySQL 8.0+
- Git

#### 安装步骤 Installation Steps

```bash
# 1. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置数据库
mysql -u root -p
CREATE DATABASE stock CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 4. 初始化数据库
python scripts/init_db.py

# 5. 启动应用
streamlit run app/main.py
```

## 📊 数据库设计 Database Design

### 核心表结构 Core Table Structure

#### 用户表 (users)

```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### 股票基础信息表 (stocks)

```sql
CREATE TABLE stocks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(20) UNIQUE NOT NULL,
    stock_name VARCHAR(100) NOT NULL,
    market VARCHAR(20) NOT NULL,
    industry VARCHAR(50),
    sector VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### 股票历史数据表 (stock_data)

```sql
CREATE TABLE stock_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,3) NOT NULL,
    high_price DECIMAL(10,3) NOT NULL,
    low_price DECIMAL(10,3) NOT NULL,
    close_price DECIMAL(10,3) NOT NULL,
    volume BIGINT DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    turnover_rate DECIMAL(8,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_date (stock_code, trade_date)
);
```

## 🔧 配置说明 Configuration

### 数据库配置 Database Configuration

#### 本地开发环境配置 Local Development Configuration

```python
# config/config.py
DATABASE_CONFIG = {
    'host': 'localhost',      # 数据库主机
    'port': 3306,            # 数据库端口
    'user': 'root',          # 数据库用户
    'password': '123456',    # 数据库密码
    'database': 'stock',     # 数据库名称（本地环境）
    'charset': 'utf8mb4'     # 字符集
}
```

#### 环境变量配置 Environment Variables

```bash
# .env 文件
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=stock
DEBUG=True
```

### 应用配置 Application Configuration

```python
# Streamlit 配置
STREAMLIT_CONFIG = {
    'server_port': 8501,
    'server_address': '0.0.0.0',
    'server_max_upload_size': 200,
    'theme_base': 'light'
}
```

## 📚 文档 Documentation

- [📖 API 文档](docs/api.md) - 详细的 API 接口文档
- [🚀 部署指南](docs/deployment.md) - 完整的部署说明
- [🧪 测试指南](tests/) - 测试用例和测试方法

## 🤝 贡献指南 Contributing

欢迎贡献代码！请遵循以下步骤：

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证 License

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 技术支持 Technical Support

如果您在使用过程中遇到问题，请：

1. 查看 [部署指南](docs/deployment.md) 中的故障排除部分
2. 检查 [API 文档](docs/api.md) 中的相关说明
3. 在 GitHub Issues 中提交问题报告

## 🎯 系统要求 System Requirements

### 最低配置 Minimum Requirements

- **操作系统**: Ubuntu 22.04 / Windows 10+ / macOS 10.15+
- **内存**: 4GB RAM
- **存储**: 10GB 可用空间
- **数据库**: MySQL 8.0

### 推荐配置 Recommended Requirements

- **操作系统**: Ubuntu 22.04 LTS
- **内存**: 8GB RAM
- **存储**: 20GB 可用空间
- **CPU**: 4 核心以上

## ✅ 项目符合性 Project Compliance

本项目完全符合以下技术要求：

### 环境要求 Environment Requirements ✅

- ✅ **系统**: Ubuntu 22.04
- ✅ **数据库**: MySQL 8.0
- ✅ **容器化**: Docker + Docker Compose

### 核心功能 Core Features ✅

- ✅ **Streamlit 应用**: 现代化 Web 界面
- ✅ **MySQL 数据库**: 完整的数据存储方案
- ✅ **用户登录**: 安全的认证系统（演示账户：admin/admin123）
- ✅ **股票分析**: 完整的技术指标分析
- ✅ **投资组合**: 专业的组合管理功能

### 数据分析功能 Data Analysis Features ✅

- ✅ **单只股票**: 日均、月均、年度、历史累计收益率分析
- ✅ **技术指标**: 最大回撤、夏普比率等风险指标
- ✅ **投资组合**: 组合收益率和风险分析
- ✅ **历史走势**: 投资组合历史表现图表

### 部署要求 Deployment Requirements ✅

- ✅ **一键部署**: `docker-compose up -d`
- ✅ **数据持久化**: MySQL 数据卷持久化
- ✅ **完整打包**: 支持 tar 格式打包
- ✅ **包含数据**: 5 年股票历史数据

详细的符合性检查报告请查看：[项目符合性报告](docs/compliance-report.md)

---

**开发团队**: Stock Analysis Team
**最后更新**: 2024 年 1 月
**版本**: v1.0.0
**符合性状态**: ✅ 完全符合要求
