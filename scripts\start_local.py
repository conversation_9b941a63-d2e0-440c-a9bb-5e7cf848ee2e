#!/usr/bin/env python3
"""
本地启动脚本
Local Startup Script

用于本地Python环境的一键启动，确保与Docker部署功能一致
支持两种模式：
1. 本地Python环境 (python scripts/start_local.py --local)
2. 本地Docker环境 (python scripts/start_local.py --docker)
"""

import os
import sys
import subprocess
import time
import argparse
import mysql.connector
from pathlib import Path

def run_command(command, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_docker():
    """检查Docker是否可用"""
    print("🔍 检查Docker环境...")
    success, stdout, stderr = run_command("docker --version")
    if not success:
        print("❌ Docker未安装或不可用")
        return False
    
    print(f"✅ Docker版本: {stdout.strip()}")
    
    success, stdout, stderr = run_command("docker-compose --version")
    if not success:
        print("❌ Docker Compose未安装或不可用")
        return False
    
    print(f"✅ Docker Compose版本: {stdout.strip()}")
    return True

def stop_existing_containers():
    """停止现有容器"""
    print("🛑 停止现有容器...")
    
    # 停止可能存在的容器
    containers = ["stock_mysql_local", "stock_app_local", "stock_mysql", "stock_app"]
    for container in containers:
        run_command(f"docker stop {container}")
        run_command(f"docker rm {container}")
    
    print("✅ 现有容器已停止")

def start_local_environment():
    """启动本地开发环境"""
    print("🚀 启动本地开发环境...")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 使用本地配置启动
    success, stdout, stderr = run_command(
        "docker-compose -f docker-compose.local.yml up -d --build",
        cwd=project_root
    )
    
    if not success:
        print(f"❌ 启动失败: {stderr}")
        return False
    
    print("✅ 容器启动成功")
    return True

def wait_for_services():
    """等待服务启动完成"""
    print("⏳ 等待服务启动完成...")
    
    # 等待MySQL服务
    max_attempts = 30
    for i in range(max_attempts):
        success, stdout, stderr = run_command(
            "docker exec stock_mysql_local mysqladmin ping -h localhost -u root -p123456"
        )
        if success:
            print("✅ MySQL服务已就绪")
            break
        time.sleep(2)
        print(f"⏳ 等待MySQL启动... ({i+1}/{max_attempts})")
    else:
        print("❌ MySQL服务启动超时")
        return False
    
    # 等待应用服务
    time.sleep(10)  # 给应用一些启动时间
    print("✅ 应用服务已启动")
    return True

def show_status():
    """显示服务状态"""
    print("\n" + "="*50)
    print("📊 服务状态")
    print("="*50)
    
    success, stdout, stderr = run_command("docker-compose -f docker-compose.local.yml ps")
    if success:
        print(stdout)
    
    print("\n🌐 访问地址:")
    print("  - 应用地址: http://localhost:8501")
    print("  - MySQL地址: localhost:3306")
    print("  - 数据库名: stock")
    print("  - 用户名: root")
    print("  - 密码: 123456")

def check_mysql_running():
    """检查MySQL是否运行"""
    print("🔍 检查MySQL服务...")

    try:
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', '123456'),
            connect_timeout=5
        )
        connection.close()
        print("✅ MySQL服务正在运行")
        return True
    except Exception as e:
        print(f"❌ MySQL服务未运行: {e}")
        return False

def start_mysql_docker():
    """启动MySQL Docker容器"""
    print("🐳 启动MySQL Docker容器...")

    try:
        # 检查容器是否已存在
        result = subprocess.run(['docker', 'ps', '-a', '--filter', 'name=mysql-stock', '--format', '{{.Names}}'],
                              capture_output=True, text=True)

        if 'mysql-stock' in result.stdout:
            print("📦 MySQL容器已存在，启动容器...")
            subprocess.run(['docker', 'start', 'mysql-stock'], check=True)
        else:
            print("📦 创建并启动MySQL容器...")
            subprocess.run([
                'docker', 'run', '-d',
                '--name', 'mysql-stock',
                '-e', 'MYSQL_ROOT_PASSWORD=123456',
                '-e', 'MYSQL_DATABASE=stock',
                '-p', '3306:3306',
                'mysql:8.0'
            ], check=True)

        # 等待MySQL启动
        print("⏳ 等待MySQL启动...")
        for i in range(30):
            if check_mysql_running():
                print("✅ MySQL容器启动成功")
                return True
            time.sleep(2)
            print(f"   等待中... ({i+1}/30)")

        print("❌ MySQL容器启动超时")
        return False

    except subprocess.CalledProcessError as e:
        print(f"❌ 启动MySQL容器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动MySQL容器出错: {e}")
        return False

def check_dependencies():
    """检查Python依赖"""
    print("🔍 检查Python依赖...")

    required_packages = [
        'streamlit', 'pandas', 'numpy', 'mysql-connector-python',
        'plotly', 'bcrypt'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("💡 请运行: pip install -r requirements.txt")
        return False

    print("✅ Python依赖检查通过")
    return True

def initialize_database():
    """初始化数据库"""
    print("🔧 初始化数据库...")

    try:
        script_path = Path(__file__).parent / "init_db.py"
        result = subprocess.run([sys.executable, str(script_path)],
                              capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 数据库初始化成功")
            return True
        else:
            print("❌ 数据库初始化失败")
            print(result.stderr)
            return False

    except Exception as e:
        print(f"❌ 数据库初始化出错: {e}")
        return False

def start_streamlit():
    """启动Streamlit应用"""
    print("🌐 启动Streamlit应用...")

    try:
        # 设置环境变量
        env = os.environ.copy()
        env.update({
            'DB_HOST': 'localhost',
            'DB_PORT': '3306',
            'DB_USER': 'root',
            'DB_PASSWORD': '123456',
            'DB_NAME': 'stock',
            'PYTHONPATH': str(Path(__file__).parent.parent)
        })

        # 启动Streamlit
        app_path = Path(__file__).parent.parent / "app" / "main.py"
        subprocess.run([
            'streamlit', 'run', str(app_path),
            '--server.port=8501',
            '--server.address=0.0.0.0',
            '--server.headless=false'
        ], env=env)

    except KeyboardInterrupt:
        print("\n🛑 应用已停止")
    except Exception as e:
        print(f"❌ 启动Streamlit失败: {e}")

def start_local_python():
    """启动本地Python环境"""
    print("🐍 股票分析系统 - 本地Python环境启动")
    print("=" * 50)

    # 检查Python依赖
    if not check_dependencies():
        sys.exit(1)

    # 检查或启动MySQL
    if not check_mysql_running():
        if not start_mysql_docker():
            print("❌ 无法启动MySQL服务")
            print("💡 请确保Docker已安装并运行")
            sys.exit(1)

    # 初始化数据库
    if not initialize_database():
        print("❌ 数据库初始化失败")
        sys.exit(1)

    print("\n" + "=" * 50)
    print("🎉 系统准备完成！")
    print("📱 访问地址: http://localhost:8501")
    print("🔑 登录账户: admin / admin123")
    print("=" * 50)

    # 启动Streamlit应用
    start_streamlit()

def start_local_docker():
    """启动本地Docker环境"""
    print("🐳 股票分析系统 - 本地Docker环境启动")
    print("=" * 50)

    # 检查Docker环境
    if not check_docker():
        sys.exit(1)

    # 停止现有容器
    stop_existing_containers()

    # 启动本地环境
    if not start_local_environment():
        sys.exit(1)

    # 等待服务启动
    if not wait_for_services():
        sys.exit(1)

    # 显示状态
    show_status()

    print("\n✅ 本地Docker环境启动完成！")
    print("💡 使用 'docker-compose -f docker-compose.local.yml down' 停止服务")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票分析系统本地启动工具')
    parser.add_argument('--mode', choices=['python', 'docker'], default='python',
                       help='启动模式: python(本地Python环境) 或 docker(本地Docker环境)')

    args = parser.parse_args()

    if args.mode == 'python':
        start_local_python()
    else:
        start_local_docker()

if __name__ == "__main__":
    main()
