#!/usr/bin/env python3
"""
本地开发环境启动脚本
Local Development Environment Startup Script

用于启动本地开发环境的便捷脚本
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(command, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_docker():
    """检查Docker是否可用"""
    print("🔍 检查Docker环境...")
    success, stdout, stderr = run_command("docker --version")
    if not success:
        print("❌ Docker未安装或不可用")
        return False
    
    print(f"✅ Docker版本: {stdout.strip()}")
    
    success, stdout, stderr = run_command("docker-compose --version")
    if not success:
        print("❌ Docker Compose未安装或不可用")
        return False
    
    print(f"✅ Docker Compose版本: {stdout.strip()}")
    return True

def stop_existing_containers():
    """停止现有容器"""
    print("🛑 停止现有容器...")
    
    # 停止可能存在的容器
    containers = ["stock_mysql_local", "stock_app_local", "stock_mysql", "stock_app"]
    for container in containers:
        run_command(f"docker stop {container}")
        run_command(f"docker rm {container}")
    
    print("✅ 现有容器已停止")

def start_local_environment():
    """启动本地开发环境"""
    print("🚀 启动本地开发环境...")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 使用本地配置启动
    success, stdout, stderr = run_command(
        "docker-compose -f docker-compose.local.yml up -d --build",
        cwd=project_root
    )
    
    if not success:
        print(f"❌ 启动失败: {stderr}")
        return False
    
    print("✅ 容器启动成功")
    return True

def wait_for_services():
    """等待服务启动完成"""
    print("⏳ 等待服务启动完成...")
    
    # 等待MySQL服务
    max_attempts = 30
    for i in range(max_attempts):
        success, stdout, stderr = run_command(
            "docker exec stock_mysql_local mysqladmin ping -h localhost -u root -p123456"
        )
        if success:
            print("✅ MySQL服务已就绪")
            break
        time.sleep(2)
        print(f"⏳ 等待MySQL启动... ({i+1}/{max_attempts})")
    else:
        print("❌ MySQL服务启动超时")
        return False
    
    # 等待应用服务
    time.sleep(10)  # 给应用一些启动时间
    print("✅ 应用服务已启动")
    return True

def show_status():
    """显示服务状态"""
    print("\n" + "="*50)
    print("📊 服务状态")
    print("="*50)
    
    success, stdout, stderr = run_command("docker-compose -f docker-compose.local.yml ps")
    if success:
        print(stdout)
    
    print("\n🌐 访问地址:")
    print("  - 应用地址: http://localhost:8501")
    print("  - MySQL地址: localhost:3306")
    print("  - 数据库名: stock")
    print("  - 用户名: root")
    print("  - 密码: 123456")

def main():
    """主函数"""
    print("🏠 股票分析系统 - 本地开发环境启动器")
    print("="*50)
    
    # 检查Docker环境
    if not check_docker():
        sys.exit(1)
    
    # 停止现有容器
    stop_existing_containers()
    
    # 启动本地环境
    if not start_local_environment():
        sys.exit(1)
    
    # 等待服务启动
    if not wait_for_services():
        sys.exit(1)
    
    # 显示状态
    show_status()
    
    print("\n✅ 本地开发环境启动完成！")
    print("💡 使用 'docker-compose -f docker-compose.local.yml down' 停止服务")

if __name__ == "__main__":
    main()
