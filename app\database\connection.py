"""
数据库连接管理模块
Database Connection Management Module

负责管理MySQL数据库连接和基础操作
"""

import mysql.connector
from mysql.connector import Error
import streamlit as st
import os
from typing import Optional, Dict, Any, List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self):
        self.connection = None
        self.config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER', 'root'),
            'password': os.getenv('DB_PASSWORD', '123456'),
            'database': os.getenv('DB_NAME', 'stock'),
            'charset': 'utf8mb4',
            'autocommit': True
        }
    
    def connect(self) -> bool:
        """建立数据库连接"""
        import time
        max_retries = 30
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                self.connection = mysql.connector.connect(**self.config)
                if self.connection.is_connected():
                    logger.info("数据库连接成功")
                    return True
            except Error as e:
                if attempt < max_retries - 1:
                    logger.warning(f"数据库连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(retry_delay)
                    continue
                else:
                    logger.error(f"数据库连接失败: {e}")
                    if 'st' in globals():
                        st.error(f"数据库连接失败: {e}")
                    return False
        return False
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def execute_query(self, query: str, params: tuple = None) -> Optional[List[Dict[str, Any]]]:
        """执行查询语句"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect():
                    return None
            
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, params)
            result = cursor.fetchall()
            cursor.close()
            return result
        except Error as e:
            logger.error(f"查询执行失败: {e}")
            st.error(f"查询执行失败: {e}")
            return None
    
    def execute_update(self, query: str, params: tuple = None) -> bool:
        """执行更新语句"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect():
                    return False
            
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            cursor.close()
            return True
        except Error as e:
            logger.error(f"更新执行失败: {e}")
            st.error(f"更新执行失败: {e}")
            return False

# 全局数据库管理器实例
db_manager = DatabaseManager()

def get_db_connection():
    """获取数据库连接"""
    return db_manager

def init_database():
    """初始化数据库连接"""
    if 'db_initialized' not in st.session_state:
        if db_manager.connect():
            st.session_state.db_initialized = True
            logger.info("数据库初始化成功")
        else:
            st.session_state.db_initialized = False
            logger.error("数据库初始化失败")

def test_connection() -> bool:
    """测试数据库连接"""
    return db_manager.connect()
