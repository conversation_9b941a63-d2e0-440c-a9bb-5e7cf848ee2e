"""
投资组合分析页面
Portfolio Analysis Page

提供投资组合创建、分析和管理功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from app.models.stock import StockManager
from app.utils.helpers import (
    format_number, format_percentage, format_currency,
    create_portfolio_pie_chart
)

def portfolio_analysis_page():
    """投资组合分析主页面"""
    
    # 页面标题
    st.markdown("""
    <div style="text-align: center; padding: 2rem 0 1rem 0;">
        <h1>💼 投资组合分析</h1>
        <p style="color: #666; font-size: 1.1rem;">构建专业投资组合，优化资产配置策略</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 初始化股票管理器
    stock_manager = StockManager()
    
    # 获取所有股票列表
    stocks = stock_manager.get_all_stocks()
    
    if not stocks:
        st.error("⚠️ 暂无股票数据，请联系管理员检查数据库连接")
        return
    
    # 创建选项卡 - 使用更好的样式
    st.markdown("### 📋 功能导航")
    tab1, tab2, tab3 = st.tabs([
        "🎯 创建投资组合", 
        "📊 组合风险分析", 
        "📈 历史表现回测"
    ])
    
    with tab1:
        create_portfolio_tab(stock_manager, stocks)
    
    with tab2:
        analyze_portfolio_tab(stock_manager)
    
    with tab3:
        portfolio_performance_tab(stock_manager)

def create_portfolio_tab(stock_manager: StockManager, stocks):
    """创建投资组合选项卡"""
    
    st.subheader("🎯 创建投资组合")
    
    # 初始化会话状态
    if 'portfolio_stocks' not in st.session_state:
        st.session_state.portfolio_stocks = []
    
    # 组合基本信息
    st.markdown("#### 📝 组合基本信息")
    
    col1, col2 = st.columns(2)
    
    with col1:
        portfolio_name = st.text_input("📝 组合名称", placeholder="例如：稳健型组合")
    
    with col2:
        portfolio_desc = st.text_input("📄 组合描述", placeholder="例如：以银行股为主的稳健投资组合")
    
    # 添加股票到组合
    st.markdown("#### 📈 添加股票")
    
    col1, col2 = st.columns(2)
    
    with col1:
        stock_options = [f"{stock.stock_code} - {stock.stock_name}" for stock in stocks]
        new_stock = st.selectbox("🎯 选择股票", options=stock_options, key="new_stock_select")
    
    with col2:
        if st.button("➕ 添加股票", use_container_width=True):
            if new_stock and new_stock not in [item['stock'] for item in st.session_state.portfolio_stocks]:
                st.session_state.portfolio_stocks.append({
                    'stock': new_stock,
                    'weight': 20.0  # 默认权重
                })
                st.success(f"已添加 {new_stock}")
                st.rerun()
            elif new_stock in [item['stock'] for item in st.session_state.portfolio_stocks]:
                st.warning("该股票已在组合中")
    
    # 显示当前组合
    if st.session_state.portfolio_stocks:
        st.markdown("#### 📊 当前组合")
        
        total_weight = 0
        for i, item in enumerate(st.session_state.portfolio_stocks):
            col1, col2, col3 = st.columns([3, 2, 1])
            
            with col1:
                st.write(f"**{item['stock']}**")
            
            with col2:
                weight = st.number_input(
                    f"⚖️ 权重 (%)",
                    min_value=0.0,
                    max_value=100.0,
                    value=item['weight'],
                    step=1.0,
                    key=f"weight_{i}"
                )
                st.session_state.portfolio_stocks[i]['weight'] = weight
                total_weight += weight
            
            with col3:
                if st.button("🗑️ 删除", key=f"remove_{i}"):
                    st.session_state.portfolio_stocks.pop(i)
                    st.rerun()
        
        # 显示总权重
        st.markdown(f"**总权重**: {total_weight:.1f}%")
        
        if abs(total_weight - 100) > 0.1:
            st.warning("⚠️ 总权重应为100%")
        
        # 创建组合按钮
        if st.button("🚀 创建投资组合", type="primary", use_container_width=True):
            if portfolio_name and len(st.session_state.portfolio_stocks) >= 2:
                if abs(total_weight - 100) < 0.1:  # 允许小的误差
                    st.success("🎉 投资组合创建成功！")
                    # 这里可以保存到数据库
                    st.balloons()
                else:
                    st.error("❌ 请确保总权重为100%")
            else:
                st.error("❌ 请填写组合名称并至少选择2只股票")
    
    # 清空组合按钮
    if st.session_state.portfolio_stocks:
        if st.button("🧹 清空组合"):
            st.session_state.portfolio_stocks = []
            st.rerun()

def analyze_portfolio_tab(stock_manager: StockManager):
    """分析投资组合选项卡"""
    
    st.subheader("📊 组合风险分析")
    
    # 检查是否有组合数据
    if 'portfolio_stocks' not in st.session_state or not st.session_state.portfolio_stocks:
        st.info("💡 请先在「创建投资组合」选项卡中创建组合")
        return
    
    # 转换组合数据格式
    portfolio_stocks = {}
    for item in st.session_state.portfolio_stocks:
        portfolio_stocks[item['stock']] = item['weight'] / 100
    
    # 显示组合构成饼图
    st.markdown("#### 🥧 组合构成分布")
    pie_fig = create_portfolio_pie_chart(
        {k: v*100 for k, v in portfolio_stocks.items()}, 
        "投资组合权重分布"
    )
    st.plotly_chart(pie_fig, use_container_width=True)
    
    # 计算组合指标
    calculate_portfolio_metrics(stock_manager, portfolio_stocks)

def calculate_portfolio_metrics(stock_manager: StockManager, portfolio_stocks):
    """计算投资组合指标"""
    
    st.markdown("#### 📈 组合风险收益指标")
    
    # 获取所有股票的收益率数据
    portfolio_returns = {}
    portfolio_metrics = {}
    
    for stock_info, weight in portfolio_stocks.items():
        stock_code = stock_info.split(' - ')[0]
        
        # 获取股票收益率指标
        returns = stock_manager.calculate_returns(stock_code, 365)  # 1年期数据
        portfolio_returns[stock_code] = returns
        portfolio_metrics[stock_info] = {
            'weight': weight * 100,
            'annual_return': returns.get('annual_return', 0),
            'volatility': returns.get('volatility', 0),
            'sharpe_ratio': returns.get('sharpe_ratio', 0),
            'max_drawdown': returns.get('max_drawdown', 0)
        }
    
    # 计算组合加权指标
    weighted_return = sum(metrics['annual_return'] * metrics['weight'] / 100 
                         for metrics in portfolio_metrics.values())
    
    weighted_volatility = np.sqrt(sum((metrics['volatility'] * metrics['weight'] / 100) ** 2 
                                     for metrics in portfolio_metrics.values()))
    
    # 计算组合夏普比率
    risk_free_rate = 3.0  # 假设无风险利率为3%
    portfolio_sharpe = (weighted_return - risk_free_rate) / weighted_volatility if weighted_volatility > 0 else 0
    
    # 显示组合整体指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("组合年化收益率", format_percentage(weighted_return))
    
    with col2:
        st.metric("组合年化波动率", format_percentage(weighted_volatility))
    
    with col3:
        st.metric("组合夏普比率", f"{portfolio_sharpe:.2f}")
    
    with col4:
        # 计算组合最大回撤（简化计算）
        avg_drawdown = sum(metrics['max_drawdown'] * metrics['weight'] / 100 
                          for metrics in portfolio_metrics.values())
        st.metric("预期最大回撤", format_percentage(avg_drawdown))
    
    # 显示个股贡献分析
    st.markdown("#### 📊 个股贡献分析")
    
    contribution_data = []
    for stock_info, metrics in portfolio_metrics.items():
        contribution_data.append({
            '股票': stock_info,
            '权重': format_percentage(metrics['weight']),
            '年化收益率': format_percentage(metrics['annual_return']),
            '收益贡献': format_percentage(metrics['annual_return'] * metrics['weight'] / 100),
            '波动率': format_percentage(metrics['volatility']),
            '夏普比率': f"{metrics['sharpe_ratio']:.2f}",
            '最大回撤': format_percentage(metrics['max_drawdown'])
        })
    
    contribution_df = pd.DataFrame(contribution_data)
    st.dataframe(contribution_df, use_container_width=True, hide_index=True)

def portfolio_performance_tab(stock_manager: StockManager):
    """投资组合历史表现选项卡"""
    
    st.subheader("📈 投资组合历史表现")
    
    # 时间范围选择
    col1, col2 = st.columns(2)
    
    with col1:
        period = st.selectbox(
            "选择分析周期",
            options=["1个月", "3个月", "6个月", "1年"],
            index=2,
            key="performance_period"
        )
    
    with col2:
        benchmark = st.selectbox(
            "选择基准",
            options=["等权重组合", "市值加权", "自定义基准"],
            index=0
        )
    
    # 使用示例组合数据
    portfolio_data = [
        {'stock': '000001 - 平安银行', 'weight': 20.0},
        {'stock': '600036 - 招商银行', 'weight': 20.0},
        {'stock': '600000 - 浦发银行', 'weight': 20.0},
        {'stock': '000002 - 万科A', 'weight': 20.0},
        {'stock': '000858 - 五粮液', 'weight': 20.0}
    ]
    
    # 计算时间范围
    end_date = date.today()
    if period == "1个月":
        start_date = end_date - timedelta(days=30)
    elif period == "3个月":
        start_date = end_date - timedelta(days=90)
    elif period == "6个月":
        start_date = end_date - timedelta(days=180)
    else:  # 1年
        start_date = end_date - timedelta(days=365)
    
    # 模拟组合历史表现
    simulate_portfolio_performance(stock_manager, portfolio_data, start_date, end_date)

def simulate_portfolio_performance(stock_manager: StockManager, portfolio_data, start_date, end_date):
    """计算真实的投资组合历史表现"""

    st.markdown("#### 📊 组合净值走势")

    # 获取所有股票的历史数据
    portfolio_nav_data = {}
    weights = {}

    # 解析组合数据
    for item in portfolio_data:
        stock_code = item['stock'].split(' - ')[0]
        weight = item['weight'] / 100  # 转换为小数
        weights[stock_code] = weight

        # 获取股票数据
        df = stock_manager.get_stock_data(stock_code, start_date, end_date)
        if not df.empty:
            # 计算归一化价格（以第一天为基准）
            normalized_prices = df['close_price'] / df['close_price'].iloc[0]
            portfolio_nav_data[stock_code] = normalized_prices

    if not portfolio_nav_data:
        st.warning("暂无足够的历史数据进行分析")
        return

    # 找到所有股票的共同交易日期
    common_dates = None
    for stock_code, prices in portfolio_nav_data.items():
        if common_dates is None:
            common_dates = set(prices.index)
        else:
            common_dates = common_dates.intersection(set(prices.index))

    if not common_dates:
        st.warning("没有找到共同的交易日期")
        return

    # 按日期排序
    common_dates = sorted(list(common_dates))

    # 计算组合净值
    portfolio_nav = []
    for date in common_dates:
        daily_nav = 0
        for stock_code, weight in weights.items():
            if stock_code in portfolio_nav_data:
                stock_nav = portfolio_nav_data[stock_code].loc[date]
                daily_nav += weight * stock_nav
        portfolio_nav.append(daily_nav)

    # 创建净值DataFrame
    nav_df = pd.DataFrame({
        'date': common_dates,
        'nav': portfolio_nav
    })
    nav_df.set_index('date', inplace=True)

    # 创建净值走势图
    import plotly.graph_objects as go
    fig = go.Figure()

    fig.add_trace(go.Scatter(
        x=nav_df.index,
        y=nav_df['nav'],
        mode='lines',
        name='组合净值',
        line=dict(color='blue', width=2)
    ))

    # 添加基准线（净值=1）
    fig.add_hline(y=1, line_dash="dash", line_color="gray",
                  annotation_text="基准线 (净值=1)")

    fig.update_layout(
        title="投资组合净值走势",
        xaxis_title="日期",
        yaxis_title="净值",
        height=400,
        showlegend=True
    )

    st.plotly_chart(fig, use_container_width=True)

    # 计算表现指标
    if len(nav_df) > 1:
        # 总收益率
        total_return = (nav_df['nav'].iloc[-1] - 1) * 100

        # 年化收益率
        days = (nav_df.index[-1] - nav_df.index[0]).days
        annual_return = ((nav_df['nav'].iloc[-1] / nav_df['nav'].iloc[0]) ** (365/days) - 1) * 100

        # 最大回撤
        cummax = nav_df['nav'].cummax()
        drawdown = (nav_df['nav'] - cummax) / cummax
        max_drawdown = drawdown.min() * 100

        # 波动率
        daily_returns = nav_df['nav'].pct_change().dropna()
        volatility = daily_returns.std() * np.sqrt(252) * 100

        # 夏普比率
        risk_free_rate = 3.0  # 假设无风险利率3%
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0

        # 显示指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("总收益率", format_percentage(total_return))

        with col2:
            st.metric("年化收益率", format_percentage(annual_return))

        with col3:
            st.metric("最大回撤", format_percentage(max_drawdown))

        with col4:
            st.metric("夏普比率", f"{sharpe_ratio:.2f}")

        # 显示详细统计
        st.markdown("#### 📈 详细统计信息")

        col1, col2 = st.columns(2)

        with col1:
            st.metric("年化波动率", format_percentage(volatility))
            st.metric("最高净值", f"{nav_df['nav'].max():.4f}")
            st.metric("最低净值", f"{nav_df['nav'].min():.4f}")

        with col2:
            # 计算胜率（上涨天数比例）
            win_rate = (daily_returns > 0).sum() / len(daily_returns) * 100
            st.metric("胜率", format_percentage(win_rate))

            # 平均日收益率
            avg_daily_return = daily_returns.mean() * 100
            st.metric("平均日收益率", format_percentage(avg_daily_return))

            # 数据天数
            st.metric("数据天数", f"{len(nav_df)}天")

        # 显示月度收益率表格
        show_monthly_returns(nav_df)

    else:
        st.warning("数据不足，无法计算表现指标")

def show_monthly_returns(nav_df: pd.DataFrame):
    """显示月度收益率表格"""

    st.markdown("#### 📅 月度收益率分析")

    # 计算月度收益率
    monthly_nav = nav_df.resample('M').last()
    monthly_returns = monthly_nav['nav'].pct_change().dropna() * 100

    if len(monthly_returns) > 0:
        # 创建月度收益率表格
        monthly_data = []
        for date, return_rate in monthly_returns.items():
            monthly_data.append({
                '年月': date.strftime('%Y-%m'),
                '月度收益率': f"{return_rate:.2f}%",
                '累计净值': f"{monthly_nav.loc[date, 'nav']:.4f}"
            })

        monthly_df = pd.DataFrame(monthly_data)
        st.dataframe(monthly_df, use_container_width=True, hide_index=True)

        # 月度收益率统计
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("平均月收益率", f"{monthly_returns.mean():.2f}%")

        with col2:
            positive_months = (monthly_returns > 0).sum()
            total_months = len(monthly_returns)
            st.metric("正收益月份", f"{positive_months}/{total_months}")

        with col3:
            st.metric("最佳月份", f"{monthly_returns.max():.2f}%")

    else:
        st.info("数据时间跨度不足一个月，无法计算月度收益率")
