# 📦 股票分析系统打包指南

## 🎯 打包要求

根据要求创建包含以下内容的tar包：
1. **已commit到git的项目代码**
2. **MySQL数据库完整数据**

## 🚀 快速打包

### 一键打包（推荐）

**Windows系统：**
```cmd
package.bat
```

**Linux/Mac系统：**
```bash
chmod +x package.sh
./package.sh
```

### 手动打包

**完整打包（包含Git代码 + MySQL数据）：**
```bash
python scripts/package_project.py
```

**简化打包（仅项目代码）：**
```bash
python scripts/package_simple.py
```

## 📋 打包前检查

运行打包测试确保环境就绪：
```bash
python scripts/test_package.py
```

检查项目包括：
- ✅ Git工具可用性
- ✅ MySQL工具可用性  
- ✅ MySQL数据库连接
- ✅ 打包功能测试

## 📦 打包输出

### 文件命名
- 格式：`stock-analysis-system_YYYYMMDD_HHMMSS.tar`
- 示例：`stock-analysis-system_20250725_143022.tar`

### 完整包内容
```
stock-analysis-system_20250725_143022.tar
├── project_code.tar          # Git归档的项目代码
├── mysql_data.sql            # MySQL数据库完整数据
└── package_info.txt          # 打包信息和部署说明
```

### 简化包内容
```
stock-analysis-system_20250725_143022.tar
├── app/                      # 应用代码
├── config/                   # 配置文件
├── scripts/                  # 脚本文件
├── requirements.txt          # Python依赖
├── docker-compose.yml        # Docker配置
├── Dockerfile               # Docker镜像
├── README.md                # 项目说明
└── DEPLOYMENT_GUIDE.md      # 部署指南
```

## 🔧 环境要求

### 基础要求
- Python 3.11+
- Git（用于代码归档）

### 完整打包额外要求
- MySQL客户端工具（mysqldump）
- 运行中的MySQL服务
- 包含完整股票数据的数据库

### 检查命令
```bash
# 检查Python
python --version

# 检查Git
git --version

# 检查MySQL工具
mysqldump --version

# 检查MySQL连接
mysql -h localhost -u root -p123456 -e "USE stock; SELECT COUNT(*) FROM stocks;"
```

## 📤 部署打包后的项目

### 解压和查看
```bash
# 1. 解压tar包
tar -xf stock-analysis-system_*.tar

# 2. 查看包内容
ls -la

# 3. 查看部署说明（如果有）
cat package_info.txt
```

### 完整包部署
```bash
# 1. 导入数据库
mysql -u root -p < mysql_data.sql

# 2. 解压项目代码
tar -xf project_code.tar

# 3. 启动应用
docker-compose up -d

# 4. 访问应用
# 浏览器打开: http://localhost:8501
# 账户: admin / admin123
```

### 简化包部署
```bash
# 1. 直接启动Docker
docker-compose up -d

# 或本地Python环境
python scripts/start_local.py --mode python

# 2. 访问应用
# 浏览器打开: http://localhost:8501
# 账户: admin / admin123
```

## ⚠️ 注意事项

### Git要求
- 确保项目已初始化为Git仓库
- 重要更改已commit到Git
- 未commit的更改会提示确认

### MySQL要求
- 确保MySQL服务正在运行
- 数据库包含完整的股票数据
- 用户有足够的权限导出数据

### 网络要求
- 如果MySQL在远程服务器，确保网络连接正常
- Docker镜像拉取需要网络连接

## 🔍 故障排除

### 常见问题

**1. Git不可用**
```bash
# 安装Git
# Windows: 下载Git for Windows
# Ubuntu: sudo apt install git
# CentOS: sudo yum install git
```

**2. MySQL工具不可用**
```bash
# 安装MySQL客户端
# Ubuntu: sudo apt install mysql-client
# CentOS: sudo yum install mysql
# Windows: 安装MySQL Workbench或MySQL Shell
```

**3. MySQL连接失败**
```bash
# 检查MySQL服务状态
docker ps | grep mysql

# 启动MySQL容器
docker start mysql-stock

# 检查连接参数
echo $DB_HOST $DB_PORT $DB_USER $DB_PASSWORD $DB_NAME
```

**4. 打包失败**
```bash
# 运行测试脚本诊断
python scripts/test_package.py

# 查看详细错误信息
python scripts/package_project.py 2>&1 | tee package.log
```

## 📞 技术支持

如遇问题，请按以下步骤排查：

1. **运行测试脚本**: `python scripts/test_package.py`
2. **检查环境要求**: 确认Git、MySQL工具已安装
3. **验证数据库**: 确认MySQL服务运行且数据完整
4. **查看错误日志**: 运行打包命令并保存输出

---

**最后更新**: 2025年7月25日  
**版本**: v1.0.0
