#!/usr/bin/env python3
"""
系统功能测试脚本
System Function Test Script

测试股票分析系统的各项功能
"""

import os
import sys
import mysql.connector
import pandas as pd
from datetime import datetime, date, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.stock import StockManager
from app.utils.helpers import (
    calculate_moving_average, calculate_rsi, calculate_macd, 
    calculate_bollinger_bands, format_percentage
)

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', '123456'),
            database=os.getenv('DB_NAME', 'stock'),
            charset='utf8mb4'
        )
        connection.close()
        print("✅ 数据库连接成功")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_stock_data():
    """测试股票数据"""
    print("🔍 测试股票数据...")
    
    try:
        stock_manager = StockManager()
        
        # 测试获取股票列表
        stocks = stock_manager.get_all_stocks()
        print(f"📊 股票总数: {len(stocks)}")
        
        if not stocks:
            print("❌ 没有找到股票数据")
            return False
        
        # 测试每只股票的数据
        for stock in stocks:
            print(f"📈 测试股票: {stock.stock_code} - {stock.stock_name}")
            
            # 测试历史数据
            df = stock_manager.get_stock_data(stock.stock_code)
            print(f"   📅 历史数据条数: {len(df)}")
            
            if len(df) < 100:
                print(f"   ⚠️ 数据量不足: {len(df)} 条")
            else:
                print(f"   ✅ 数据充足: {len(df)} 条")
            
            # 测试最新价格
            latest_price = stock_manager.get_latest_price(stock.stock_code)
            print(f"   💰 最新价格: {latest_price}")
            
            # 测试收益率计算
            returns = stock_manager.calculate_returns(stock.stock_code, 30)
            print(f"   📈 30日收益率: {format_percentage(returns.get('total_return', 0))}")
        
        print("✅ 股票数据测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 股票数据测试失败: {e}")
        return False

def test_technical_indicators():
    """测试技术指标计算"""
    print("🔍 测试技术指标计算...")
    
    try:
        stock_manager = StockManager()
        stocks = stock_manager.get_all_stocks()
        
        if not stocks:
            print("❌ 没有股票数据用于测试")
            return False
        
        # 使用第一只股票进行测试
        test_stock = stocks[0]
        df = stock_manager.get_stock_data(test_stock.stock_code)
        
        if len(df) < 50:
            print("❌ 数据不足，无法测试技术指标")
            return False
        
        close_prices = pd.to_numeric(df['close_price'], errors='coerce').dropna()
        
        print(f"📊 使用 {test_stock.stock_code} 测试技术指标")
        
        # 测试移动平均线
        ma5 = calculate_moving_average(close_prices, 5)
        ma20 = calculate_moving_average(close_prices, 20)
        print(f"   📈 MA5 最新值: {ma5.iloc[-1]:.2f}")
        print(f"   📈 MA20 最新值: {ma20.iloc[-1]:.2f}")
        
        # 测试RSI
        rsi = calculate_rsi(close_prices)
        print(f"   📊 RSI 最新值: {rsi.iloc[-1]:.2f}")
        
        # 测试MACD
        macd_data = calculate_macd(close_prices)
        print(f"   📈 MACD 最新值: {macd_data['macd'].iloc[-1]:.4f}")
        print(f"   📈 Signal 最新值: {macd_data['signal'].iloc[-1]:.4f}")
        
        # 测试布林带
        bollinger = calculate_bollinger_bands(close_prices)
        print(f"   📏 布林上轨: {bollinger['upper'].iloc[-1]:.2f}")
        print(f"   📏 布林下轨: {bollinger['lower'].iloc[-1]:.2f}")
        
        print("✅ 技术指标计算测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 技术指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_portfolio_calculation():
    """测试投资组合计算"""
    print("🔍 测试投资组合计算...")
    
    try:
        stock_manager = StockManager()
        stocks = stock_manager.get_all_stocks()
        
        if len(stocks) < 2:
            print("❌ 股票数量不足，无法测试投资组合")
            return False
        
        # 创建测试组合
        portfolio_data = [
            {'stock': f'{stocks[0].stock_code} - {stocks[0].stock_name}', 'weight': 50.0},
            {'stock': f'{stocks[1].stock_code} - {stocks[1].stock_name}', 'weight': 50.0}
        ]
        
        print(f"📊 测试组合: {portfolio_data[0]['stock']} (50%) + {portfolio_data[1]['stock']} (50%)")
        
        # 测试组合净值计算
        end_date = date.today()
        start_date = end_date - timedelta(days=90)
        
        portfolio_nav_data = {}
        weights = {}
        
        for item in portfolio_data:
            stock_code = item['stock'].split(' - ')[0]
            weight = item['weight'] / 100
            weights[stock_code] = weight
            
            df = stock_manager.get_stock_data(stock_code, start_date, end_date)
            if not df.empty:
                normalized_prices = df['close_price'] / df['close_price'].iloc[0]
                portfolio_nav_data[stock_code] = normalized_prices
        
        if len(portfolio_nav_data) == 2:
            print("✅ 投资组合数据获取成功")
            
            # 计算组合净值
            common_dates = None
            for stock_code, prices in portfolio_nav_data.items():
                if common_dates is None:
                    common_dates = set(prices.index)
                else:
                    common_dates = common_dates.intersection(set(prices.index))
            
            if common_dates:
                print(f"📅 共同交易日: {len(common_dates)} 天")
                print("✅ 投资组合计算测试完成")
                return True
            else:
                print("❌ 没有找到共同交易日")
                return False
        else:
            print("❌ 投资组合数据不完整")
            return False
        
    except Exception as e:
        print(f"❌ 投资组合测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始系统功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 测试数据库连接
    test_results.append(("数据库连接", test_database_connection()))
    
    # 测试股票数据
    test_results.append(("股票数据", test_stock_data()))
    
    # 测试技术指标
    test_results.append(("技术指标", test_technical_indicators()))
    
    # 测试投资组合
    test_results.append(("投资组合", test_portfolio_calculation()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查系统配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
