-- 股票分析系统示例数据
-- Sample Data for Stock Analysis System

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

USE stock;

-- 删除现有数据（注意外键约束顺序）
DELETE FROM portfolio_holdings;
DELETE FROM user_portfolios;
DELETE FROM stock_data;
DELETE FROM stocks;
DELETE FROM users;

-- 插入用户数据
-- 密码哈希对应: admin123, password123, password456
INSERT INTO users (username, password_hash, email) VALUES
('admin', '$2b$12$sj2VLjK018g.RAbVZvJx2uhiEOJPcA0GrqjO1RZgyfIO5zwfClLx2', '<EMAIL>'),
('user1', '$2b$12$EbsKi93qKNFSDMUNOgky7.wL4nIXaRYQ4.DcHecUb1GX/HpUNf9mO', '<EMAIL>'),
('user2', '$2b$12$tl8FvGg6wxMh9On6BjHaX.JL291O6zu2aD2Qx4QaRh7Ky8VqGqKOm', '<EMAIL>');

-- 插入股票基础信息
INSERT INTO stocks (stock_code, stock_name, industry, market, sector, is_active) VALUES
('000001', '平安银行', '银行', 'SZ', '金融服务', TRUE),
('000002', '万科A', '房地产开发', 'SZ', '房地产', TRUE),
('000858', '五粮液', '白酒制造', 'SZ', '食品饮料', TRUE),
('600000', '浦发银行', '银行', 'SH', '金融服务', TRUE),
('600036', '招商银行', '银行', 'SH', '金融服务', TRUE);

-- 插入股票交易数据 - 5年历史数据 (2019-2024)
-- 注意：这里只显示部分数据，完整数据将通过Python脚本生成

-- 插入一些基础数据以确保系统能正常启动
INSERT INTO stock_data (stock_code, stock_name, industry, trade_date, open_price, close_price, high_price, low_price, volume, amount, turnover_rate, pe_ratio) VALUES
('000001', '平安银行', '银行', '2024-01-15', 13.20, 13.35, 13.45, 13.15, 125000000, 1668750000.00, 4.71, 8.55),
('000002', '万科A', '房地产开发', '2024-01-15', 8.95, 9.05, 9.12, 8.88, 98000000, 885900000.00, 4.62, 5.81),
('000858', '五粮液', '白酒制造', '2024-01-15', 162.50, 164.50, 166.80, 161.20, 45000000, 7402500000.00, 4.25, 65.11),
('600000', '浦发银行', '银行', '2024-01-15', 7.65, 7.70, 7.78, 7.62, 156000000, 1201200000.00, 5.48, 144.92),
('600036', '招商银行', '银行', '2024-01-15', 36.80, 37.05, 37.25, 36.65, 89000000, 3297450000.00, 2.92, 21.54);

-- 创建示例投资组合
INSERT INTO user_portfolios (user_id, portfolio_name, description) VALUES
(2, '稳健型组合', '以银行股为主的稳健投资组合'),
(2, '成长型组合', '包含消费和科技股的成长型组合');

-- 插入投资组合持仓数据
INSERT INTO portfolio_holdings (portfolio_id, stock_code, weight) VALUES
(1, '000001', 0.2000), -- 平安银行 20%
(1, '600000', 0.2000), -- 浦发银行 20%
(1, '600036', 0.2000), -- 招商银行 20%
(1, '000002', 0.2000), -- 万科A 20%
(1, '000858', 0.2000); -- 五粮液 20%


