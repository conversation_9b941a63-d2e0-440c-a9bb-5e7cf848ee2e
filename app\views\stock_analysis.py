"""
股票分析页面
Stock Analysis Page

提供单只股票的详细分析功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from app.models.stock import StockManager
from app.utils.helpers import (
    format_number, format_percentage, format_currency,
    display_metric_cards, create_candlestick_chart, create_line_chart,
    validate_date_range, calculate_period_return, create_technical_chart,
    calculate_moving_average, calculate_rsi, calculate_macd, calculate_bollinger_bands
)

def stock_analysis_page():
    """股票分析主页面"""
    
    # 页面标题
    st.markdown("""
    <div style="text-align: center; padding: 2rem 0 1rem 0;">
        <h1>📈 股票深度分析</h1>
        <p style="color: #666; font-size: 1.1rem;">专业的股票技术分析和投资决策支持</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 初始化股票管理器
    stock_manager = StockManager()
    
    # 获取所有股票列表
    stocks = stock_manager.get_all_stocks()
    
    if not stocks:
        st.error("⚠️ 暂无股票数据，请联系管理员检查数据库连接")
        return
    
    # 股票选择器 - 使用更好的布局
    with st.container():
        st.markdown("### 🎯 选择分析标的")
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            stock_options = [f"{stock.stock_code} - {stock.stock_name}" for stock in stocks]
            selected_stock = st.selectbox(
                "请选择要分析的股票",
                options=stock_options,
                index=0,
                help="选择您要进行深度分析的股票"
            )
        
        with col2:
            st.markdown("<br>", unsafe_allow_html=True)
            if st.button("🔄 刷新股票列表", use_container_width=True):
                st.cache_data.clear()
                st.rerun()
    
    if not selected_stock:
        st.warning("请选择要分析的股票")
        return
    
    # 解析选择的股票
    stock_code = selected_stock.split(' - ')[0]
    stock_name = selected_stock.split(' - ')[1]
    
    # 获取股票对象
    selected_stock_obj = None
    for stock in stocks:
        if stock.stock_code == stock_code:
            selected_stock_obj = stock
            break
    
    if not selected_stock_obj:
        st.error("无法找到选择的股票信息")
        return
    
    # 显示股票基本信息
    show_stock_info(selected_stock_obj, stock_manager)
    
    st.markdown("---")
    
    # 分析参数设置
    show_analysis_controls(stock_manager, stock_code, stock_name)

def show_stock_info(stock: object, stock_manager: StockManager):
    """显示股票基本信息"""
    
    st.markdown("### 📋 股票基本信息")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.info(f"**股票代码**\n{stock.stock_code}")
    
    with col2:
        st.info(f"**股票名称**\n{stock.stock_name}")
    
    with col3:
        st.info(f"**所属行业**\n{stock.industry or 'N/A'}")
    
    with col4:
        st.info(f"**交易市场**\n{stock.market}")
    
    # 获取最新价格和基本指标
    latest_price = stock_manager.get_latest_price(stock.stock_code)
    returns_30d = stock_manager.calculate_returns(stock.stock_code, 30)
    
    # 显示关键指标
    st.markdown("### 📊 关键指标")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "最新价格",
            format_currency(latest_price) if latest_price else "N/A",
            help="最新交易日收盘价"
        )
    
    with col2:
        st.metric(
            "30日收益率",
            format_percentage(returns_30d.get('total_return', 0)),
            help="最近30个交易日的总收益率"
        )
    
    with col3:
        st.metric(
            "年化波动率",
            format_percentage(returns_30d.get('volatility', 0)),
            help="基于30日数据计算的年化波动率"
        )
    
    with col4:
        st.metric(
            "夏普比率",
            f"{returns_30d.get('sharpe_ratio', 0):.2f}",
            help="风险调整后的收益率指标"
        )

def show_analysis_controls(stock_manager: StockManager, stock_code: str, stock_name: str):
    """显示分析控制面板"""
    
    st.markdown("### ⚙️ 分析参数设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        period = st.selectbox(
            "📅 选择分析周期",
            options=["1个月", "3个月", "6个月", "1年", "自定义"],
            index=2
        )
    
    with col2:
        chart_type = st.selectbox(
            "📊 图表类型",
            options=["K线图", "折线图", "技术指标综合图"],
            index=2
        )
    
    # 计算日期范围
    end_date = date.today()
    
    if period == "1个月":
        start_date = end_date - timedelta(days=30)
    elif period == "3个月":
        start_date = end_date - timedelta(days=90)
    elif period == "6个月":
        start_date = end_date - timedelta(days=180)
    elif period == "1年":
        start_date = end_date - timedelta(days=365)
    else:  # 自定义
        st.markdown("#### 📅 自定义日期范围")
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("📅 开始日期", value=end_date - timedelta(days=90))
        with col2:
            end_date = st.date_input("📅 结束日期", value=end_date)
        
        if not validate_date_range(start_date, end_date):
            return
    
    # 显示分析结果
    show_analysis_results(stock_manager, stock_code, stock_name, start_date, end_date, chart_type)

def show_analysis_results(stock_manager: StockManager, stock_code: str, stock_name: str, 
                         start_date: date, end_date: date, chart_type: str):
    """显示分析结果"""
    
    # 获取股票数据
    df = stock_manager.get_stock_data(stock_code, start_date, end_date)
    
    if df.empty:
        st.warning(f"在指定时间范围内没有找到 {stock_code} 的数据")
        return
    
    st.markdown("### 📈 价格走势分析")
    
    # 显示图表
    if chart_type == "K线图":
        fig = create_candlestick_chart(df, f"{stock_name} ({stock_code}) K线图")
    elif chart_type == "技术指标综合图":
        fig = create_technical_chart(df, f"{stock_name} ({stock_code})")
    else:
        fig = create_line_chart(df, 'close_price', f"{stock_name} ({stock_code}) 价格走势")

    st.plotly_chart(fig, use_container_width=True)

    # 显示技术指标数值
    if not df.empty and len(df) > 20:
        show_technical_indicators(df, stock_name)
    
    # 计算分析期间的收益率指标
    period_days = (end_date - start_date).days
    returns = stock_manager.calculate_returns(stock_code, period_days)
    
    # 显示收益率分析
    st.markdown("### 📊 收益率分析")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "期间总收益率",
            format_percentage(returns.get('total_return', 0)),
            help=f"{period_days}天期间的总收益率"
        )
    
    with col2:
        st.metric(
            "年化收益率",
            format_percentage(returns.get('annual_return', 0)),
            help="基于期间收益率计算的年化收益率"
        )
    
    with col3:
        st.metric(
            "最大回撤",
            format_percentage(returns.get('max_drawdown', 0)),
            help="期间内的最大回撤幅度"
        )
    
    # 显示风险分析
    st.markdown("### ⚖️ 风险分析")
    
    volatility = returns.get('volatility', 0)
    sharpe_ratio = returns.get('sharpe_ratio', 0)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric(
            "年化波动率",
            format_percentage(volatility),
            help="衡量价格波动程度的指标"
        )
        
        # 风险等级评估
        if volatility < 15:
            risk_level = "🟢 低风险"
        elif volatility < 25:
            risk_level = "🟡 中等风险"
        else:
            risk_level = "🔴 高风险"
        
        st.info(f"**风险等级**: {risk_level}")
    
    with col2:
        st.metric(
            "夏普比率",
            f"{sharpe_ratio:.2f}",
            help="风险调整后的收益率指标"
        )
        
        # 夏普比率评估
        if sharpe_ratio > 1:
            sharpe_assessment = "🟢 优秀"
        elif sharpe_ratio > 0.5:
            sharpe_assessment = "🟡 良好"
        elif sharpe_ratio > 0:
            sharpe_assessment = "🟠 一般"
        else:
            sharpe_assessment = "🔴 较差"
        
        st.info(f"**表现评级**: {sharpe_assessment}")
    
    # 显示数据表格
    st.markdown("### 📋 历史数据")
    
    # 格式化数据表格
    display_df = df.tail(10).copy()  # 显示最近10天数据
    display_df = display_df[['open_price', 'high_price', 'low_price', 'close_price', 'volume']]
    display_df.columns = ['开盘价', '最高价', '最低价', '收盘价', '成交量']
    
    # 格式化价格列
    for col in ['开盘价', '最高价', '最低价', '收盘价']:
        display_df[col] = display_df[col].apply(lambda x: f"¥{x:.2f}")
    
    # 格式化成交量
    display_df['成交量'] = display_df['成交量'].apply(format_number)
    
    st.dataframe(display_df, use_container_width=True)

def show_technical_indicators(df: pd.DataFrame, stock_name: str):
    """显示技术指标数值"""

    st.markdown("### 📊 技术指标分析")

    # 计算技术指标
    close_prices = pd.to_numeric(df['close_price'], errors='coerce').dropna()

    if len(close_prices) < 20:
        st.warning("数据不足，无法计算技术指标")
        return

    # 移动平均线
    ma5 = calculate_moving_average(close_prices, 5).iloc[-1]
    ma10 = calculate_moving_average(close_prices, 10).iloc[-1]
    ma20 = calculate_moving_average(close_prices, 20).iloc[-1]

    # RSI
    rsi = calculate_rsi(close_prices).iloc[-1]

    # MACD
    macd_data = calculate_macd(close_prices)
    macd_current = macd_data['macd'].iloc[-1]
    signal_current = macd_data['signal'].iloc[-1]

    # 布林带
    bollinger = calculate_bollinger_bands(close_prices)
    bb_upper = bollinger['upper'].iloc[-1]
    bb_middle = bollinger['middle'].iloc[-1]
    bb_lower = bollinger['lower'].iloc[-1]

    # 当前价格
    current_price = close_prices.iloc[-1]

    # 显示移动平均线
    st.markdown("#### 📈 移动平均线")
    col1, col2, col3 = st.columns(3)

    with col1:
        ma5_trend = "🔴" if current_price < ma5 else "🟢"
        st.metric("MA5", f"¥{ma5:.2f}", f"{ma5_trend} {((current_price/ma5-1)*100):+.2f}%")

    with col2:
        ma10_trend = "🔴" if current_price < ma10 else "🟢"
        st.metric("MA10", f"¥{ma10:.2f}", f"{ma10_trend} {((current_price/ma10-1)*100):+.2f}%")

    with col3:
        ma20_trend = "🔴" if current_price < ma20 else "🟢"
        st.metric("MA20", f"¥{ma20:.2f}", f"{ma20_trend} {((current_price/ma20-1)*100):+.2f}%")

    # 显示RSI和MACD
    st.markdown("#### 📊 动量指标")
    col1, col2, col3 = st.columns(3)

    with col1:
        rsi_status = "超买" if rsi > 70 else "超卖" if rsi < 30 else "正常"
        rsi_color = "🔴" if rsi > 70 else "🟢" if rsi < 30 else "🟡"
        st.metric("RSI (14)", f"{rsi:.1f}", f"{rsi_color} {rsi_status}")

    with col2:
        macd_trend = "🟢" if macd_current > signal_current else "🔴"
        st.metric("MACD", f"{macd_current:.3f}", f"{macd_trend}")

    with col3:
        signal_trend = "🟢" if macd_current > signal_current else "🔴"
        st.metric("MACD Signal", f"{signal_current:.3f}", f"{signal_trend}")

    # 显示布林带
    st.markdown("#### 📏 布林带分析")
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("布林上轨", f"¥{bb_upper:.2f}")

    with col2:
        st.metric("布林中轨", f"¥{bb_middle:.2f}")

    with col3:
        st.metric("布林下轨", f"¥{bb_lower:.2f}")

    # 布林带位置分析
    bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
    if bb_position > 0.8:
        bb_status = "🔴 接近上轨，可能超买"
    elif bb_position < 0.2:
        bb_status = "🟢 接近下轨，可能超卖"
    else:
        bb_status = "🟡 在正常区间内"

    st.info(f"**布林带位置**: {bb_status} (位置: {bb_position:.1%})")

    # 综合技术分析建议
    st.markdown("#### 🎯 技术分析建议")

    signals = []

    # MA信号
    if current_price > ma5 > ma10 > ma20:
        signals.append("🟢 多头排列，趋势向上")
    elif current_price < ma5 < ma10 < ma20:
        signals.append("🔴 空头排列，趋势向下")
    else:
        signals.append("🟡 均线纠缠，趋势不明")

    # RSI信号
    if rsi > 70:
        signals.append("🔴 RSI超买，注意回调风险")
    elif rsi < 30:
        signals.append("🟢 RSI超卖，可能反弹")

    # MACD信号
    if macd_current > signal_current and macd_current > 0:
        signals.append("🟢 MACD金叉且在零轴上方，看涨")
    elif macd_current < signal_current and macd_current < 0:
        signals.append("🔴 MACD死叉且在零轴下方，看跌")

    for signal in signals:
        st.write(f"• {signal}")

    if not signals:
        st.info("📊 当前技术指标信号不明确，建议观望")
