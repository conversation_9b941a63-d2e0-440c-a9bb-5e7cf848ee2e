#!/usr/bin/env python3
"""
打包测试脚本
Package Test Script

测试打包功能是否正常工作
"""

import os
import sys
import subprocess
import tarfile
import tempfile
from pathlib import Path

def test_git_availability():
    """测试Git是否可用"""
    print("🔍 测试Git可用性...")
    
    try:
        result = subprocess.run(['git', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Git可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ Git不可用")
            return False
    except Exception as e:
        print(f"❌ Git测试失败: {e}")
        return False

def test_mysql_availability():
    """测试MySQL工具是否可用"""
    print("🔍 测试MySQL工具可用性...")
    
    try:
        result = subprocess.run(['mysqldump', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ mysqldump可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ mysqldump不可用")
            return False
    except Exception as e:
        print(f"❌ mysqldump测试失败: {e}")
        return False

def test_mysql_connection():
    """测试MySQL连接"""
    print("🔍 测试MySQL连接...")
    
    try:
        import mysql.connector
        
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', '123456'),
            database=os.getenv('DB_NAME', 'stock'),
            connect_timeout=5
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM stocks")
        stock_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM stock_data")
        data_count = cursor.fetchone()[0]
        
        cursor.close()
        connection.close()
        
        print(f"✅ MySQL连接成功")
        print(f"   股票数量: {stock_count}")
        print(f"   数据条数: {data_count}")
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False

def test_simple_package():
    """测试简化打包"""
    print("🔍 测试简化打包...")
    
    try:
        # 运行简化打包脚本
        script_path = Path(__file__).parent / "package_simple.py"
        result = subprocess.run([sys.executable, str(script_path)], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 简化打包测试成功")
            
            # 查找生成的包文件
            for file in Path('.').glob('stock-analysis-system_*.tar'):
                print(f"   生成的包: {file}")
                
                # 测试包内容
                try:
                    with tarfile.open(file, 'r') as tar:
                        members = tar.getnames()
                        print(f"   包含 {len(members)} 个文件")
                        
                        # 检查关键文件
                        key_files = ['app/main.py', 'requirements.txt', 'docker-compose.yml']
                        for key_file in key_files:
                            if key_file in members:
                                print(f"   ✅ 包含: {key_file}")
                            else:
                                print(f"   ❌ 缺少: {key_file}")
                    
                    # 清理测试文件
                    file.unlink()
                    print(f"   清理测试文件: {file}")
                    
                except Exception as e:
                    print(f"   ❌ 包内容检查失败: {e}")
                
                break
            
            return True
        else:
            print(f"❌ 简化打包失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 简化打包测试失败: {e}")
        return False

def test_full_package():
    """测试完整打包"""
    print("🔍 测试完整打包...")
    
    try:
        # 运行完整打包脚本
        script_path = Path(__file__).parent / "package_project.py"
        result = subprocess.run([sys.executable, str(script_path)], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ 完整打包测试成功")
            
            # 查找生成的包文件
            for file in Path('.').glob('stock-analysis-system_*.tar'):
                print(f"   生成的包: {file}")
                
                # 测试包内容
                try:
                    with tarfile.open(file, 'r') as tar:
                        members = tar.getnames()
                        print(f"   包含 {len(members)} 个文件")
                        
                        # 检查关键文件
                        key_files = ['project_code.tar', 'mysql_data.sql', 'package_info.txt']
                        for key_file in key_files:
                            if key_file in members:
                                print(f"   ✅ 包含: {key_file}")
                            else:
                                print(f"   ❌ 缺少: {key_file}")
                    
                    # 清理测试文件
                    file.unlink()
                    print(f"   清理测试文件: {file}")
                    
                except Exception as e:
                    print(f"   ❌ 包内容检查失败: {e}")
                
                break
            
            return True
        else:
            print(f"❌ 完整打包失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 完整打包测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 股票分析系统打包功能测试")
    print("=" * 50)
    
    tests = [
        ("Git可用性", test_git_availability),
        ("MySQL工具", test_mysql_availability),
        ("MySQL连接", test_mysql_connection),
        ("简化打包", test_simple_package),
    ]
    
    results = []
    
    for name, test_func in tests:
        print(f"\n🔍 {name}测试:")
        result = test_func()
        results.append((name, result))
    
    # 如果MySQL可用，测试完整打包
    mysql_available = results[1][1] and results[2][1]  # MySQL工具 + 连接
    if mysql_available:
        print(f"\n🔍 完整打包测试:")
        full_result = test_full_package()
        results.append(("完整打包", full_result))
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📋 测试结果:")
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if result:
            passed += 1
    
    success_rate = passed / len(results) * 100
    print(f"\n🎯 通过率: {passed}/{len(results)} ({success_rate:.1f}%)")
    
    if passed >= len(results) - 1:  # 允许一个测试失败
        print("\n🎉 打包功能测试基本通过！")
        print("💡 推荐的打包方式:")
        if mysql_available:
            print("   🔥 完整打包: python scripts/package_project.py")
        print("   📦 简化打包: python scripts/package_simple.py")
        print("   🚀 一键打包: package.bat (Windows) 或 ./package.sh (Linux/Mac)")
        return True
    else:
        print("\n⚠️ 打包功能测试失败，请检查环境配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
