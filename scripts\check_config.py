#!/usr/bin/env python3
"""
配置检查脚本
Configuration Check Script

检查本地配置是否正确设置
"""

import os
import sys
import mysql.connector
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from config.config import get_config

def check_environment_variables():
    """检查环境变量"""
    print("🔍 检查环境变量...")
    
    env_vars = {
        'DB_HOST': os.getenv('DB_HOST', '未设置'),
        'DB_PORT': os.getenv('DB_PORT', '未设置'),
        'DB_USER': os.getenv('DB_USER', '未设置'),
        'DB_PASSWORD': os.getenv('DB_PASSWORD', '未设置'),
        'DB_NAME': os.getenv('DB_NAME', '未设置'),
        'DEBUG': os.getenv('DEBUG', '未设置'),
    }
    
    print("📋 环境变量状态:")
    for key, value in env_vars.items():
        status = "✅" if value != "未设置" else "❌"
        print(f"  {status} {key}: {value}")
    
    return all(value != "未设置" for value in env_vars.values())

def check_config_file():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    try:
        config = get_config()
        print("📋 配置文件状态:")
        print(f"  ✅ 数据库主机: {config.DB_HOST}")
        print(f"  ✅ 数据库端口: {config.DB_PORT}")
        print(f"  ✅ 数据库用户: {config.DB_USER}")
        print(f"  ✅ 数据库名称: {config.DB_NAME}")
        print(f"  ✅ 调试模式: {config.DEBUG}")
        return True
    except Exception as e:
        print(f"  ❌ 配置文件错误: {e}")
        return False

def check_database_connection():
    """检查数据库连接"""
    print("\n🔍 检查数据库连接...")
    
    config = get_config()
    
    try:
        # 尝试连接数据库
        connection = mysql.connector.connect(
            host=config.DB_HOST,
            port=config.DB_PORT,
            user=config.DB_USER,
            password=config.DB_PASSWORD
        )
        
        if connection.is_connected():
            print("  ✅ 数据库服务器连接成功")
            
            # 检查数据库是否存在
            cursor = connection.cursor()
            cursor.execute(f"SHOW DATABASES LIKE '{config.DB_NAME}'")
            result = cursor.fetchone()
            
            if result:
                print(f"  ✅ 数据库 '{config.DB_NAME}' 存在")
                
                # 连接到指定数据库
                connection.database = config.DB_NAME
                
                # 检查表是否存在
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                if tables:
                    print(f"  ✅ 数据库包含 {len(tables)} 个表")
                    for table in tables:
                        print(f"    - {table[0]}")
                else:
                    print("  ⚠️ 数据库为空，需要初始化")
                    
            else:
                print(f"  ❌ 数据库 '{config.DB_NAME}' 不存在")
                return False
            
            cursor.close()
            connection.close()
            return True
            
    except mysql.connector.Error as e:
        print(f"  ❌ 数据库连接失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 连接检查出错: {e}")
        return False

def check_docker_files():
    """检查Docker配置文件"""
    print("\n🔍 检查Docker配置文件...")
    
    project_root = Path(__file__).parent.parent
    
    files_to_check = [
        'docker-compose.yml',
        'docker-compose.local.yml',
        'Dockerfile',
        '.env'
    ]
    
    all_exist = True
    for file_name in files_to_check:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"  ✅ {file_name} 存在")
        else:
            print(f"  ❌ {file_name} 不存在")
            all_exist = False
    
    return all_exist

def provide_recommendations():
    """提供配置建议"""
    print("\n💡 配置建议:")
    print("1. 确保MySQL服务正在运行")
    print("2. 检查数据库用户权限")
    print("3. 运行数据库初始化脚本: python scripts/init_db.py")
    print("4. 使用本地Docker配置: docker-compose -f docker-compose.local.yml up -d")
    print("5. 或使用便捷脚本: python scripts/start_local.py")

def main():
    """主函数"""
    print("🏠 股票分析系统 - 配置检查工具")
    print("="*50)
    
    # 检查环境变量
    env_ok = check_environment_variables()
    
    # 检查配置文件
    config_ok = check_config_file()
    
    # 检查数据库连接
    db_ok = check_database_connection()
    
    # 检查Docker文件
    docker_ok = check_docker_files()
    
    print("\n" + "="*50)
    print("📊 检查结果汇总:")
    print(f"  {'✅' if env_ok else '❌'} 环境变量配置")
    print(f"  {'✅' if config_ok else '❌'} 配置文件")
    print(f"  {'✅' if db_ok else '❌'} 数据库连接")
    print(f"  {'✅' if docker_ok else '❌'} Docker配置文件")
    
    if all([env_ok, config_ok, db_ok, docker_ok]):
        print("\n🎉 所有配置检查通过！系统已准备就绪。")
    else:
        print("\n⚠️ 发现配置问题，请根据上述检查结果进行修复。")
        provide_recommendations()

if __name__ == "__main__":
    main()
