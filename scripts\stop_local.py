#!/usr/bin/env python3
"""
本地开发环境停止脚本
Local Development Environment Stop Script

用于停止本地开发环境的便捷脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def stop_local_environment():
    """停止本地开发环境"""
    print("🛑 停止本地开发环境...")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 停止本地配置的服务
    success, stdout, stderr = run_command(
        "docker-compose -f docker-compose.local.yml down",
        cwd=project_root
    )
    
    if not success:
        print(f"❌ 停止失败: {stderr}")
        return False
    
    print("✅ 本地环境已停止")
    return True

def cleanup_containers():
    """清理容器"""
    print("🧹 清理容器...")
    
    # 停止并删除可能存在的容器
    containers = ["stock_mysql_local", "stock_app_local", "stock_mysql", "stock_app"]
    for container in containers:
        run_command(f"docker stop {container}")
        run_command(f"docker rm {container}")
    
    print("✅ 容器清理完成")

def cleanup_volumes():
    """清理数据卷（可选）"""
    print("🗑️  是否要清理数据卷？这将删除所有数据库数据！")
    response = input("输入 'yes' 确认清理数据卷，或按回车跳过: ").strip().lower()
    
    if response == 'yes':
        success, stdout, stderr = run_command("docker volume rm stock_mysql_local_data")
        if success:
            print("✅ 数据卷已清理")
        else:
            print("⚠️ 数据卷清理失败或不存在")
    else:
        print("⏭️ 跳过数据卷清理")

def show_status():
    """显示当前状态"""
    print("\n" + "="*50)
    print("📊 当前Docker状态")
    print("="*50)
    
    success, stdout, stderr = run_command("docker ps -a --filter name=stock")
    if success:
        print(stdout)
    
    print("\n📦 相关数据卷:")
    success, stdout, stderr = run_command("docker volume ls --filter name=stock")
    if success:
        print(stdout)

def main():
    """主函数"""
    print("🏠 股票分析系统 - 本地开发环境停止器")
    print("="*50)
    
    # 停止本地环境
    stop_local_environment()
    
    # 清理容器
    cleanup_containers()
    
    # 可选清理数据卷
    cleanup_volumes()
    
    # 显示状态
    show_status()
    
    print("\n✅ 本地开发环境已停止！")

if __name__ == "__main__":
    main()
