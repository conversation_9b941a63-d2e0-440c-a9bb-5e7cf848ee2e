# 使用官方 Python 3.11-slim 镜像作为基础
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONPATH=/app

# 替换 APT 源为阿里云，安装系统依赖
RUN rm -rf /etc/apt/sources.list.d/* && \
    echo "deb https://mirrors.aliyun.com/debian bookworm main contrib non-free" > /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian bookworm-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security bookworm-security main contrib non-free" >> /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        gcc \
        g++ \
        curl \
        wget \
        git \
        build-essential \
        ca-certificates \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# 拷贝依赖文件
COPY requirements.txt .

# 安装 Python 依赖，使用清华 PyPI 镜像源
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt \
    -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 拷贝项目所有文件到镜像中（确保所有文件都被复制）
COPY . .

# 确保scripts目录有执行权限
RUN chmod +x scripts/*.py 2>/dev/null || true

# 创建必要目录
RUN mkdir -p /app/data /app/logs

# 设置容器对外端口
EXPOSE 8501

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl --fail http://localhost:8501/_stcore/health || exit 1

# 创建启动脚本
COPY scripts/start_app.sh /app/start_app.sh
RUN chmod +x /app/start_app.sh

# 创建非 root 用户并设置权限
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 启动应用
CMD ["/app/start_app.sh"]
