services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: stock_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: stock
    ports:
      - "3306:3306"
    volumes:
      - stock_mysql_data:/var/lib/mysql
      - ./scripts/init_database.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./scripts/sample_data.sql:/docker-entrypoint-initdb.d/02-sample.sql:ro
    networks:
      - stock_network
    healthcheck:
      test:
        [
          "CMD",
          "mysqladmin",
          "ping",
          "-h",
          "localhost",
          "-u",
          "root",
          "-p123456",
        ]
      timeout: 20s
      retries: 30
      interval: 3s
      start_period: 60s

  # Streamlit 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: stock_app
    restart: always
    ports:
      - "8501:8501"
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: root
      DB_PASSWORD: 123456
      DB_NAME: stock
      PYTHONPATH: /app
      PYTHONUNBUFFERED: 1
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - stock_network
    volumes:
      - ./data:/app/data

volumes:
  stock_mysql_data:
    driver: local

networks:
  stock_network:
    driver: bridge
