# Docker 部署说明

## 🚀 快速启动

### 1. 清理并启动服务

```bash
# 清理现有容器和数据
docker-compose down -v

# 启动服务
docker-compose up --build
```

### 2. 等待服务就绪

- MySQL 初始化需要 1-2 分钟
- 应用启动后会自动重试连接数据库
- 看到 "You can now view your Streamlit app" 表示启动成功

### 3. 访问应用

浏览器打开：http://localhost:8501

### 4. 停止服务

```bash
docker-compose down
```

## 📋 服务说明

- **MySQL**: 端口 3306，数据库 `stock`，用户 `root`，密码 `123456`
- **Streamlit**: 端口 8501，股票分析应用
- **自动初始化**: 数据库结构和示例数据自动创建

## 👥 默认账号

| 用户名 | 密码        | 说明       |
| ------ | ----------- | ---------- |
| admin  | admin123    | 管理员账户 |
| user1  | password123 | 普通用户   |
| user2  | password456 | 普通用户   |

## 🔧 常用命令

```bash
# 后台启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 完全清理
docker-compose down -v
```

## 🔍 故障排除

### 常见问题

1. **MySQL 初始化时间长**

   - 首次启动需要 1-2 分钟初始化数据库
   - 请耐心等待，不要中断启动过程

2. **应用连接数据库失败**

   - 应用会自动重试连接 30 次
   - 如果仍然失败，检查 MySQL 容器是否正常运行

3. **端口冲突**
   - 确保端口 3306 和 8501 未被占用
   - 可以修改 docker-compose.yml 中的端口映射

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs mysql
docker-compose logs app

# 实时查看日志
docker-compose logs -f
```

### 重新初始化

如果遇到数据库问题，可以完全重新初始化：

```bash
# 停止并删除所有数据
docker-compose down -v

# 删除镜像（可选）
docker rmi stock-analysis-app-app

# 重新构建和启动
docker-compose up --build
```
