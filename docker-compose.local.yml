# 本地开发环境 Docker Compose 配置
# Local Development Environment Docker Compose Configuration

services:
  # MySQL 数据库服务 - 本地开发配置
  mysql:
    image: mysql:8.0
    container_name: stock_mysql_local
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: stock
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - stock_mysql_local_data:/var/lib/mysql
      - ./config/mysql.cnf:/etc/mysql/conf.d/mysql.cnf:ro
      - ./scripts/init_database.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./scripts/sample_data.sql:/docker-entrypoint-initdb.d/02-sample.sql:ro
    networks:
      - stock_local_network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --default-authentication-plugin=mysql_native_password
    healthcheck:
      test:
        [
          "CMD",
          "mysqladmin",
          "ping",
          "-h",
          "localhost",
          "-u",
          "root",
          "-p123456",
        ]
      timeout: 20s
      retries: 10
      interval: 30s
      start_period: 30s

  # Streamlit 应用服务 - 本地开发配置
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: stock_app_local
    restart: always
    ports:
      - "8501:8501"
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: root
      DB_PASSWORD: 123456
      DB_NAME: stock
      PYTHONPATH: /app
      PYTHONUNBUFFERED: 1
      DEBUG: "True"
      FLASK_ENV: development
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - stock_local_network
    volumes:
      - ./data:/app/data
      - ./app:/app/app:ro  # 挂载应用代码用于开发调试
      - ./config:/app/config:ro  # 挂载配置文件

volumes:
  stock_mysql_local_data:
    driver: local

networks:
  stock_local_network:
    driver: bridge
