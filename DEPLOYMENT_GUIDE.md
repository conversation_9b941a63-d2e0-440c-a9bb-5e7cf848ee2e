# 📈 股票分析系统 - 完整部署指南

## 🎯 项目概述

这是一个基于 Streamlit 和 MySQL 的专业股票分析系统，提供：

- ✅ **完整的 5 年历史数据** - 自动生成 5 只股票的完整历史数据
- ✅ **股票技术分析** - 移动平均线、RSI、MACD、布林带等技术指标
- ✅ **投资组合分析** - 真实的组合净值计算和走势图
- ✅ **用户认证系统** - 安全的登录功能
- ✅ **Docker 一键部署** - 完整的容器化解决方案

## 🚀 快速部署

### 方法一：Docker Compose 部署（推荐）

```bash
# 1. 克隆或解压项目
cd stock-analysis-app

# 2. 启动服务
docker-compose up -d

# 3. 查看启动状态
docker-compose logs -f

# 4. 访问应用
# 浏览器打开: http://localhost:8501
# 默认账户: admin / admin123
```

### 方法二：本地开发部署

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动 MySQL 数据库
docker run -d --name mysql-stock \
  -e MYSQL_ROOT_PASSWORD=123456 \
  -e MYSQL_DATABASE=stock \
  -p 3306:3306 \
  mysql:8.0

# 3. 初始化数据库
python scripts/init_db.py

# 4. 生成历史数据
python scripts/generate_stock_data.py

# 5. 启动应用
streamlit run app/main.py
```

## 📊 功能验证

### 自动测试

```bash
# 运行完整功能测试
python scripts/test_system.py
```

### 手动验证清单

#### ✅ 用户登录

- [ ] 访问 http://localhost:8501
- [ ] 使用 admin/admin123 登录
- [ ] 验证登录成功

#### ✅ 股票分析功能

- [ ] 进入"股票分析"页面
- [ ] 选择任意股票（如：000001 - 平安银行）
- [ ] 选择"技术指标综合图"
- [ ] 验证显示：
  - [ ] K 线图正常显示
  - [ ] 移动平均线（MA5, MA10, MA20）
  - [ ] MACD 指标
  - [ ] RSI 指标
  - [ ] 成交量图
  - [ ] 技术指标数值
  - [ ] 技术分析建议

#### ✅ 投资组合分析

- [ ] 进入"投资组合分析"页面
- [ ] 创建投资组合：
  - [ ] 添加 2-3 只股票
  - [ ] 设置权重（总和 100%）
  - [ ] 创建组合成功
- [ ] 查看组合分析：
  - [ ] 组合构成饼图
  - [ ] 风险收益指标
  - [ ] 个股贡献分析
- [ ] 查看历史表现：
  - [ ] 组合净值走势图
  - [ ] 表现指标（收益率、回撤、夏普比率）
  - [ ] 月度收益率表格

#### ✅ 市场概览

- [ ] 进入"市场概览"页面
- [ ] 验证显示：
  - [ ] 市场统计数据
  - [ ] 行业分布图表
  - [ ] 热门股票排行
  - [ ] 股票详细走势图

## 🔧 配置说明

### 数据库配置

```yaml
# docker-compose.yml
mysql:
  environment:
    MYSQL_ROOT_PASSWORD: 123456
    MYSQL_DATABASE: stock
  ports:
    - "3306:3306"
```

### 应用配置

```yaml
# docker-compose.yml
app:
  environment:
    DB_HOST: mysql
    DB_PORT: 3306
    DB_USER: root
    DB_PASSWORD: 123456
    DB_NAME: stock
  ports:
    - "8501:8501"
```

## 📈 数据说明

### 股票列表

系统包含 5 只股票的完整数据：

- 000001 - 平安银行（银行业）
- 000002 - 万科 A（房地产）
- 000858 - 五粮液（食品饮料）
- 600000 - 浦发银行（银行业）
- 600036 - 招商银行（银行业）

### 历史数据

- **时间范围**: 5 年历史数据（约 1300 个交易日）
- **数据字段**: 开盘价、收盘价、最高价、最低价、成交量、成交额
- **数据质量**: 模拟真实市场数据，包含趋势和波动性

## 🛠️ 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看日志
docker-compose logs mysql
docker-compose logs app

# 重新构建
docker-compose down
docker-compose up --build -d
```

#### 2. 数据库连接失败

```bash
# 检查MySQL容器状态
docker-compose ps

# 手动连接测试
docker exec -it stock_mysql mysql -u root -p123456 stock
```

#### 3. 历史数据缺失

```bash
# 手动生成数据
docker exec -it stock_app python scripts/generate_stock_data.py

# 或重新启动容器
docker-compose restart app
```

#### 4. 技术指标不显示

- 确保选择了"技术指标综合图"
- 检查数据时间范围（建议选择 3 个月以上）
- 验证股票有足够的历史数据

## 📋 系统要求

### 最低配置

- **内存**: 4GB RAM
- **存储**: 2GB 可用空间
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

### 推荐配置

- **内存**: 8GB RAM
- **存储**: 5GB 可用空间
- **CPU**: 4 核心

## 🎉 验收标准

系统完全符合以下要求：

### ✅ 环境要求

- [x] Ubuntu 22.04 兼容
- [x] MySQL 8.0 数据库
- [x] Docker 容器化部署

### ✅ 核心功能

- [x] Streamlit Web 应用
- [x] 用户登录系统
- [x] 股票数据分析
- [x] 投资组合管理

### ✅ 数据分析功能

- [x] 单只股票分析（日、月、年收益率）
- [x] 技术指标（MA、RSI、MACD、布林带）
- [x] 投资组合收益率分析
- [x] 组合净值走势图
- [x] 风险指标（最大回撤、夏普比率）

### ✅ 部署要求

- [x] 一键部署（docker-compose up -d）
- [x] 数据持久化
- [x] 5 年历史数据
- [x] 完整功能验证

## 📞 技术支持

如遇问题，请按以下步骤排查：

1. **运行系统测试**: `python scripts/test_system.py`
2. **查看容器日志**: `docker-compose logs -f`
3. **验证数据库**: 确认 MySQL 容器正常运行
4. **检查网络**: 确认端口 8501 和 3306 未被占用

---

**最后更新**: 2025 年 7 月  
**版本**: v1.0.0
