#!/usr/bin/env python3
"""
快速验证脚本
Quick Verification Script

快速验证系统的核心功能是否正常
"""

import os
import sys
import time
import requests
import mysql.connector
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_database():
    """检查数据库连接和数据"""
    print("🔍 检查数据库...")
    
    try:
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', '123456'),
            database=os.getenv('DB_NAME', 'stock'),
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查股票数量
        cursor.execute("SELECT COUNT(*) FROM stocks")
        stock_count = cursor.fetchone()[0]
        print(f"   📊 股票数量: {stock_count}")
        
        # 检查历史数据数量
        cursor.execute("SELECT COUNT(*) FROM stock_data")
        data_count = cursor.fetchone()[0]
        print(f"   📈 历史数据条数: {data_count}")
        
        # 检查数据日期范围
        cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM stock_data")
        date_range = cursor.fetchone()
        print(f"   📅 数据日期范围: {date_range[0]} 到 {date_range[1]}")
        
        cursor.close()
        connection.close()
        
        # 验证数据充足性
        if stock_count >= 5 and data_count >= 1000:
            print("   ✅ 数据库检查通过")
            return True
        else:
            print("   ❌ 数据不足")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        return False

def check_web_app():
    """检查Web应用是否正常运行"""
    print("🔍 检查Web应用...")
    
    try:
        # 检查应用是否启动
        response = requests.get("http://localhost:8501", timeout=10)
        if response.status_code == 200:
            print("   ✅ Web应用正常运行")
            return True
        else:
            print(f"   ❌ Web应用响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 无法连接到Web应用")
        return False
    except requests.exceptions.Timeout:
        print("   ❌ Web应用响应超时")
        return False
    except Exception as e:
        print(f"   ❌ Web应用检查失败: {e}")
        return False

def check_stock_analysis():
    """检查股票分析功能"""
    print("🔍 检查股票分析功能...")
    
    try:
        from app.models.stock import StockManager
        from app.utils.helpers import calculate_moving_average, calculate_rsi
        
        stock_manager = StockManager()
        
        # 获取股票列表
        stocks = stock_manager.get_all_stocks()
        if not stocks:
            print("   ❌ 无法获取股票列表")
            return False
        
        # 测试第一只股票
        test_stock = stocks[0]
        print(f"   📊 测试股票: {test_stock.stock_code}")
        
        # 获取历史数据
        df = stock_manager.get_stock_data(test_stock.stock_code)
        if len(df) < 100:
            print(f"   ❌ 历史数据不足: {len(df)} 条")
            return False
        
        # 测试技术指标计算
        import pandas as pd
        close_prices = pd.to_numeric(df['close_price'], errors='coerce').dropna()
        
        ma5 = calculate_moving_average(close_prices, 5)
        rsi = calculate_rsi(close_prices)
        
        if len(ma5) > 0 and len(rsi) > 0:
            print(f"   📈 MA5最新值: {ma5.iloc[-1]:.2f}")
            print(f"   📊 RSI最新值: {rsi.iloc[-1]:.2f}")
            print("   ✅ 股票分析功能正常")
            return True
        else:
            print("   ❌ 技术指标计算失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 股票分析功能检查失败: {e}")
        return False

def check_portfolio_analysis():
    """检查投资组合分析功能"""
    print("🔍 检查投资组合分析功能...")
    
    try:
        from app.models.stock import StockManager
        from datetime import date, timedelta
        
        stock_manager = StockManager()
        stocks = stock_manager.get_all_stocks()
        
        if len(stocks) < 2:
            print("   ❌ 股票数量不足，无法测试组合")
            return False
        
        # 创建测试组合
        end_date = date.today()
        start_date = end_date - timedelta(days=90)
        
        portfolio_nav_data = {}
        for i, stock in enumerate(stocks[:2]):  # 使用前两只股票
            df = stock_manager.get_stock_data(stock.stock_code, start_date, end_date)
            if not df.empty:
                normalized_prices = df['close_price'] / df['close_price'].iloc[0]
                portfolio_nav_data[stock.stock_code] = normalized_prices
        
        if len(portfolio_nav_data) == 2:
            # 计算组合净值
            common_dates = None
            for stock_code, prices in portfolio_nav_data.items():
                if common_dates is None:
                    common_dates = set(prices.index)
                else:
                    common_dates = common_dates.intersection(set(prices.index))
            
            if common_dates and len(common_dates) > 10:
                print(f"   📅 组合数据天数: {len(common_dates)}")
                print("   ✅ 投资组合分析功能正常")
                return True
            else:
                print("   ❌ 组合数据不足")
                return False
        else:
            print("   ❌ 无法获取组合数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 投资组合分析功能检查失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 股票分析系统快速验证")
    print("=" * 50)
    print(f"⏰ 验证时间: {datetime.now()}")
    print()
    
    checks = [
        ("数据库", check_database),
        ("Web应用", check_web_app),
        ("股票分析", check_stock_analysis),
        ("投资组合", check_portfolio_analysis)
    ]
    
    results = []
    
    for name, check_func in checks:
        print(f"🔍 {name}检查:")
        result = check_func()
        results.append((name, result))
        print()
    
    # 输出总结
    print("=" * 50)
    print("📋 验证结果:")
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if result:
            passed += 1
    
    success_rate = passed / len(results) * 100
    print(f"\n🎯 通过率: {passed}/{len(results)} ({success_rate:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 系统验证通过！所有功能正常")
        print("📱 访问地址: http://localhost:8501")
        print("🔑 登录账户: admin / admin123")
        return True
    else:
        print("\n⚠️ 系统验证失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
