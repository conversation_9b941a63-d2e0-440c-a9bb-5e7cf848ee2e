#!/usr/bin/env python3
"""
简化打包脚本
Simple Packaging Script

创建包含项目代码和数据库脚本的tar包
适用于没有运行MySQL服务的情况
"""

import os
import sys
import subprocess
import tarfile
import tempfile
from datetime import datetime
from pathlib import Path

def check_git_status():
    """检查git状态"""
    print("🔍 检查Git状态...")
    
    try:
        result = subprocess.run(['git', 'status'], 
                              capture_output=True, text=True, cwd='.')
        
        if result.returncode != 0:
            print("❌ 当前目录不是Git仓库")
            return False
        
        print("✅ Git状态检查完成")
        return True
        
    except Exception as e:
        print(f"❌ Git检查失败: {e}")
        return False

def create_package():
    """创建简化包"""
    print("📦 创建项目包...")
    
    try:
        # 生成包名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        package_name = f"stock-analysis-system_{timestamp}.tar"
        
        # 获取git信息
        try:
            git_commit = subprocess.run(['git', 'rev-parse', 'HEAD'], 
                                      capture_output=True, text=True).stdout.strip()
            git_branch = subprocess.run(['git', 'branch', '--show-current'], 
                                      capture_output=True, text=True).stdout.strip()
        except:
            git_commit = "unknown"
            git_branch = "unknown"
        
        # 创建tar包
        with tarfile.open(package_name, 'w') as tar:
            
            # 添加项目文件（排除不需要的文件）
            exclude_patterns = {
                '__pycache__', '.git', '.gitignore', 
                'node_modules', '.env', '*.pyc', '*.pyo',
                'logs', 'tmp', 'temp'
            }
            
            def should_exclude(path):
                path_str = str(path)
                for pattern in exclude_patterns:
                    if pattern in path_str:
                        return True
                return False
            
            # 添加主要目录和文件
            important_paths = [
                'app/',
                'config/',
                'scripts/',
                'requirements.txt',
                'docker-compose.yml',
                'Dockerfile',
                'README.md',
                'DEPLOYMENT_GUIDE.md'
            ]
            
            for path_str in important_paths:
                path = Path(path_str)
                if path.exists():
                    if path.is_file():
                        tar.add(path, arcname=path_str)
                        print(f"   添加文件: {path_str}")
                    else:
                        for root, dirs, files in os.walk(path):
                            # 过滤目录
                            dirs[:] = [d for d in dirs if not should_exclude(Path(root) / d)]
                            
                            for file in files:
                                file_path = Path(root) / file
                                if not should_exclude(file_path):
                                    arcname = str(file_path).replace('\\', '/')
                                    tar.add(file_path, arcname=arcname)
                                    print(f"   添加文件: {arcname}")
        
        # 检查包大小
        package_size = Path(package_name).stat().st_size
        print(f"✅ 项目包已创建: {package_name}")
        print(f"   包大小: {package_size:,} bytes ({package_size/1024/1024:.1f} MB)")
        
        # 创建部署说明
        readme_content = f"""# 股票分析系统部署包

## 包信息
- 打包时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Git分支: {git_branch}
- Git提交: {git_commit}

## 部署步骤

### 方法一：Docker部署（推荐）
```bash
# 1. 解压包
tar -xf {package_name}

# 2. 进入目录
cd stock-analysis-system/

# 3. 启动服务
docker-compose up -d

# 4. 访问应用
# 浏览器打开: http://localhost:8501
# 账户: admin / admin123
```

### 方法二：本地Python环境
```bash
# 1. 解压包
tar -xf {package_name}

# 2. 进入目录
cd stock-analysis-system/

# 3. 安装依赖
pip install -r requirements.txt

# 4. 启动（自动配置MySQL和数据）
python scripts/start_local.py --mode python

# 5. 访问应用
# 浏览器打开: http://localhost:8501
# 账户: admin / admin123
```

## 功能特性
- ✅ 完整的5年股票历史数据
- ✅ 股票技术分析（MA、RSI、MACD、布林带）
- ✅ 投资组合分析和净值走势
- ✅ 用户认证系统
- ✅ 一键部署

## 技术支持
如有问题，请查看 DEPLOYMENT_GUIDE.md 或运行：
```bash
python scripts/quick_verify.py
```
"""
        
        with open(f"README_{timestamp}.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ 部署说明已创建: README_{timestamp}.txt")
        
        return package_name
        
    except Exception as e:
        print(f"❌ 创建包失败: {e}")
        return None

def main():
    """主函数"""
    print("📦 股票分析系统简化打包工具")
    print("=" * 50)
    
    # 检查git状态
    if not check_git_status():
        print("⚠️ 继续创建包（不包含git信息）...")
    
    # 创建包
    package_name = create_package()
    if not package_name:
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print(f"📦 包文件: {package_name}")
    print("\n💡 部署说明:")
    print(f"   1. 解压: tar -xf {package_name}")
    print("   2. Docker部署: docker-compose up -d")
    print("   3. 本地部署: python scripts/start_local.py --mode python")
    print("   4. 访问: http://localhost:8501 (admin/admin123)")

if __name__ == "__main__":
    main()
