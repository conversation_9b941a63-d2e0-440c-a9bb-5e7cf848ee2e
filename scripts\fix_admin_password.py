#!/usr/bin/env python3
"""
修复管理员密码脚本
Fix Admin Password Script
"""

import bcrypt
import mysql.connector
import os

def generate_password_hash(password: str) -> str:
    """生成密码哈希"""
    salt = bcrypt.gensalt()
    password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
    return password_hash.decode('utf-8')

def fix_admin_password():
    """修复管理员密码"""
    try:
        # 直接使用环境变量连接数据库
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'mysql'),
            port=int(os.getenv('DB_PORT', 3306)),
            user=os.getenv('DB_USER', 'stock_user'),
            password=os.getenv('DB_PASSWORD', '123456'),
            database=os.getenv('DB_NAME', 'stock_analysis')
        )
        cursor = connection.cursor()
        
        # 生成新的密码哈希
        new_password_hash = generate_password_hash("admin123")
        
        # 更新管理员密码
        update_query = """
        UPDATE users 
        SET password_hash = %s 
        WHERE username = 'admin'
        """
        cursor.execute(update_query, (new_password_hash,))
        connection.commit()
        
        print("✅ 管理员密码已成功修复")
        print(f"用户名: admin")
        print(f"密码: admin123")
        print(f"新哈希: {new_password_hash}")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    fix_admin_password()
