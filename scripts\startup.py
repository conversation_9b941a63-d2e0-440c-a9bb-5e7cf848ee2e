#!/usr/bin/env python3
"""
应用启动脚本
Application Startup Script

在应用启动时检查并生成必要的数据
"""

import os
import sys
import time
import subprocess
import mysql.connector
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def wait_for_database(max_attempts=30):
    """等待数据库就绪"""
    print("等待数据库连接...")
    
    for attempt in range(max_attempts):
        try:
            connection = mysql.connector.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                port=int(os.getenv('DB_PORT', 3306)),
                user=os.getenv('DB_USER', 'root'),
                password=os.getenv('DB_PASSWORD', '123456'),
                database=os.getenv('DB_NAME', 'stock'),
                charset='utf8mb4'
            )
            connection.close()
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"⏳ 数据库连接尝试 {attempt + 1}/{max_attempts}: {e}")
            time.sleep(2)
    
    print("❌ 数据库连接失败")
    return False

def check_data_exists():
    """检查是否已有足够的历史数据"""
    try:
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', '123456'),
            database=os.getenv('DB_NAME', 'stock'),
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM stock_data")
        count = cursor.fetchone()[0]
        
        cursor.close()
        connection.close()
        
        # 如果数据少于1000条，认为需要生成数据
        return count > 1000
        
    except Exception as e:
        print(f"检查数据时出错: {e}")
        return False

def generate_historical_data():
    """生成历史数据"""
    print("🔄 开始生成历史数据...")
    
    try:
        # 运行数据生成脚本
        script_path = os.path.join(os.path.dirname(__file__), 'generate_stock_data.py')
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 历史数据生成成功")
            print(result.stdout)
            return True
        else:
            print("❌ 历史数据生成失败")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 数据生成超时")
        return False
    except Exception as e:
        print(f"❌ 数据生成出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 股票分析系统启动中...")
    print(f"⏰ 启动时间: {datetime.now()}")
    
    # 等待数据库就绪
    if not wait_for_database():
        print("❌ 数据库连接失败，退出启动")
        sys.exit(1)
    
    # 检查是否需要生成数据
    if not check_data_exists():
        print("📊 检测到数据不足，开始生成历史数据...")
        if not generate_historical_data():
            print("⚠️ 历史数据生成失败，但系统将继续启动")
    else:
        print("✅ 历史数据已存在，跳过生成步骤")
    
    print("🎉 系统启动准备完成")

if __name__ == "__main__":
    main()
